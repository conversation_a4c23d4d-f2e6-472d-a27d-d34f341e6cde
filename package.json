{"name": "netone-selfcare", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start --dev-client", "android": "DARK_MODE=media expo run:android", "ios": "DARK_MODE=media expo run:ios", "web": "DARK_MODE=media expo start --web"}, "dependencies": {"@expo/html-elements": "^0.4.2", "@gluestack-ui/accordion": "^1.0.14", "@gluestack-ui/actionsheet": "^0.2.53", "@gluestack-ui/alert": "^0.1.16", "@gluestack-ui/alert-dialog": "^0.1.38", "@gluestack-ui/avatar": "^0.1.18", "@gluestack-ui/button": "^1.0.14", "@gluestack-ui/checkbox": "^0.1.39", "@gluestack-ui/divider": "^0.1.10", "@gluestack-ui/fab": "^0.1.28", "@gluestack-ui/form-control": "^0.1.19", "@gluestack-ui/icon": "^0.1.27", "@gluestack-ui/image": "^0.1.17", "@gluestack-ui/input": "^0.1.38", "@gluestack-ui/link": "^0.1.29", "@gluestack-ui/menu": "^0.2.43", "@gluestack-ui/modal": "^0.1.41", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/popover": "^0.1.49", "@gluestack-ui/pressable": "^0.1.23", "@gluestack-ui/progress": "^0.1.18", "@gluestack-ui/radio": "^0.1.40", "@gluestack-ui/select": "^0.1.31", "@gluestack-ui/slider": "^0.1.32", "@gluestack-ui/spinner": "^0.1.15", "@gluestack-ui/switch": "^0.1.29", "@gluestack-ui/textarea": "^0.1.25", "@gluestack-ui/toast": "^1.0.9", "@gluestack-ui/tooltip": "^0.1.44", "@legendapp/motion": "^2.4.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "11.4.1", "@react-navigation/bottom-tabs": "^6.5.9", "@react-navigation/native": "^6.1.8", "@react-navigation/native-stack": "^6.9.13", "@shopify/flash-list": "1.7.6", "axios": "^1.4.0", "babel-plugin-module-resolver": "^5.0.2", "base-64": "^1.0.0", "deprecated-react-native-prop-types": "^4.0.0", "expo": "^53.0.11", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.6", "expo-clipboard": "~7.1.4", "expo-constants": "~17.1.6", "expo-contacts": "~14.2.5", "expo-font": "~13.3.1", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "jwt-decode": "^4.0.0", "lottie-react-native": "7.2.2", "native-base": "^3.4.28", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.79.3", "react-native-actions-sheet": "^0.8.26", "react-native-animatable": "^1.4.0", "react-native-css-interop": "^0.1.22", "react-native-device-info": "^10.11.0", "react-native-gesture-handler": "~2.24.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-material-ripple": "^0.9.1", "react-native-reanimated": "^3.18.0", "react-native-root-siblings": "^4.1.1", "react-native-root-toast": "^3.4.1", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "~4.11.1", "react-native-size-matters": "^0.4.0", "react-native-svg": "^15.2.0", "react-native-user-agent": "^2.3.1", "styled-components": "^6.0.7", "tailwindcss": "^3.4.17", "zustand": "^4.5.2"}, "devDependencies": {"@babel/core": "^7.24.0", "@types/react": "~19.0.10", "jscodeshift": "^0.15.2"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["jwt-decode", "react-native-keyboard-aware-scroll-view", "react-native-material-ripple"], "listUnknownPackages": false}}}, "private": true, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}