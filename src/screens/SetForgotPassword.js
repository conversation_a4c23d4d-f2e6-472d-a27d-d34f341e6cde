import { Feather } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { Box } from "@/components/ui/box";
import { Button } from "@/components/ui/button";
import { FormControl } from "@/components/ui/form-control";
import { Heading } from "@/components/ui/heading";
import { Input, InputField } from "@/components/ui/input";
import { ScrollView } from "@/components/ui/scroll-view";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { View } from "@/components/ui/view";
import React, { useEffect, useState } from "react";
import { StyleSheet } from "react-native";
import Toast from "react-native-root-toast";
import { moderateScale, scale, verticalScale } from "react-native-size-matters";
import LoadingModal from "../components/Loading/LoadingModal";
import AuthService from "../services/Auth/Auth";

const SetForgotPassword = (props) => {
  const [oldPin, setOldPin] = useState("");
  const [newPin, setNewPin] = useState("");
  const [showpin, setShowpin] = useState(true);
  const [newPinError, setNewPinError] = useState(false);
  const [confirmPin, setConfirmPin] = useState("");
  const [showConfirmPin, setShowConfirmPin] = useState(true);
  const [confirmPinError, setConfirmPinError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [failed, setFailed] = useState(false);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const navigation = useNavigation();
  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [failed]);

  const validate = () => {
    if (!newPin) {
      setNewPinError("This field are required");
      return false;
    }
    if (!confirmPin) {
      setConfirmPinError("This field are required");
      return false;
    }
    if (newPin !== confirmPin) {
      setConfirmPinError("New PIN and Confirm PIN do not match");
      return false;
    }
    return true;
  };

  const handleChangePin = async () => {
    try {
      const res = validate();
      if (!res) {
        return;
      }
      setLoading(true);
      const data = {
        password: newPin,
        token: props.route.params.data,
      };

      const response = await AuthService.forgotPassword(data);
      if (response.data.success) {
        setSuccess(true);
        setSuccessMessage("Failed to change password, Please try again");
        navigation.navigate("Login");
      } else {
        setFailed(true);
        setErrorMessage("Failed to change password, Please try again");
      }
      setNewPin("");
      setConfirmPin("");
      setErrorMessage("");
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setFailed(true);

      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  return (
    <ScrollView w="100%" bg={"white"} h={"100%"} px={moderateScale(24)}>
      <Toast
        visible={failed}
        position={30}
        shadow={true}
        animation={true}
        hideOnPress={true}
        backgroundColor={"red"}
        opacity={0.9}>
        {errorMessage}
      </Toast>

      <Toast
        visible={success}
        position={30}
        shadow={true}
        animation={true}
        hideOnPress={true}
        backgroundColor={"green"}
        opacity={0.9}>
        {successMessage}
      </Toast>
      <View pt={moderateScale(24)}>
        <LoadingModal isLoading={loading} />

        <Box safeArea w="100%">
          <Heading
            fontFamily={"openSansSemiBold"}
            fontSize={moderateScale(20)}
            fontWeight="600"
            color="coolGray.800">
            Set New Password
          </Heading>

          <VStack space={moderateScale(16)} mt={moderateScale(48)}>
            <FormControl>
              <View className="relative">
                <Input
                  className={`${
                    newPinError ? "border-red-400" : "border-gray-300"
                  } bg-gray-100 rounded-lg pr-12`}
                  style={{ height: verticalScale(48) }}>
                  <InputField
                    secureTextEntry={showpin}
                    onFocus={() => {
                      setNewPinError("");
                    }}
                    placeholder="New Password"
                    onChangeText={(text) => setNewPin(text)}
                    keyboardType={"default"}
                    style={{
                      fontFamily: "openSansSemiBold",
                      fontSize: moderateScale(15),
                    }}
                  />
                </Input>
                <View className="absolute right-3 top-0 h-full justify-center">
                  <Feather
                    name={showpin ? "eye-off" : "eye"}
                    size={moderateScale(20)}
                    color="gray"
                    onPress={() => {
                      setShowpin(!showpin);
                    }}
                  />
                </View>
              </View>
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {newPinError}
              </Text>
            </FormControl>
            <FormControl>
              <View className="relative">
                <Input
                  className={`${
                    confirmPinError ? "border-red-400" : "border-gray-300"
                  } bg-gray-100 rounded-lg pr-12`}
                  style={{ height: verticalScale(48) }}>
                  <InputField
                    secureTextEntry={showConfirmPin}
                    onFocus={() => {
                      setConfirmPinError("");
                    }}
                    placeholder="Confirm Password"
                    onChangeText={(text) => setConfirmPin(text)}
                    keyboardType={"default"}
                    style={{
                      fontFamily: "openSansSemiBold",
                      fontSize: moderateScale(15),
                    }}
                  />
                </Input>
                <View className="absolute right-3 top-0 h-full justify-center">
                  <Feather
                    name={showConfirmPin ? "eye-off" : "eye"}
                    size={moderateScale(20)}
                    color="gray"
                    onPress={() => {
                      setShowConfirmPin(!showConfirmPin);
                    }}
                  />
                </View>
              </View>
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {confirmPinError}
              </Text>
            </FormControl>

            <Button
              mt={moderateScale(24)}
              w={"100%"}
              rounded={"lg"}
              onPress={() => handleChangePin()}
              h={verticalScale(48)}
              bg="#F68C1E">
              <Text fontSize={moderateScale(15)} color="white">
                Set Password
              </Text>
            </Button>
          </VStack>
        </Box>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    padding: 20,
  },
  label: {
    fontSize: 16,
    marginTop: 20,
  },
  input: {
    width: "100%",
    height: 40,
    borderColor: "gray",
    borderWidth: 1,
    marginTop: 10,
    padding: 10,
  },
  button: {
    marginTop: 20,
    backgroundColor: "blue",
    padding: 10,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
  },
  errorMessage: {
    marginTop: 20,
    color: "red",
    fontSize: 16,
  },
});

export default SetForgotPassword;
