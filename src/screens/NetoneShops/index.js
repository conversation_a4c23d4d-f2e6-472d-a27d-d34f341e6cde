import { <PERSON><PERSON><PERSON>, <PERSON>ather } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { Center } from "@/components/ui/center";
import { Divider } from "@/components/ui/divider";
import { HStack } from "@/components/ui/hstack";
import { Input, InputField } from "@/components/ui/input";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { View } from "@/components/ui/view";
import React, { useEffect, useState } from "react";
import { FlatList, StyleSheet, TouchableOpacity } from "react-native";
import { moderateScale, verticalScale, scale } from "react-native-size-matters";
import LocalStore from "../../utilities/store";

const Shops = ({ route }) => {
  const navigation = useNavigation();
  const [profile, setProfile] = useState(null);
  useEffect(() => {
    const getProfile = async () => {
      const profile = await LocalStore.getData("@userProfile");

      if (profile !== null) {
        setProfile(profile);
      }
    };
    getProfile();
  }, []);
  const data = [
    {
      Column1: "Machipisa",
      Address: "Stand  No.985 Machipisa, Highfields, Harare",
      Latitude: -17.885574,
      Longitude: 30.982561,
      Zone: 1,
    },
    {
      Column1: "Chiredzi",
      Address: "Shop Number, Old Mutual Complex, Chiredzi",
      Latitude: -21.044631,
      Longitude: 31.669666,
      Zone: 5,
    },
    {
      Column1: "Karoi",
      Address: "No 1 Roseway Street Petrotrade Complex Karoi",
      Latitude: -16.819875,
      Longitude: 29.687256,
      Zone: 4,
    },
    {
      Column1: "Kwekwe",
      Address: "Shop Number 1, CAIPF Building, Kwekwe",
      Latitude: -18.929146,
      Longitude: 29.807421,
      Zone: 5,
    },
    {
      Column1: "Marondera",
      Address: "1137 Second St, CAIPF Building,  Marondera",
      Latitude: -18.192057,
      Longitude: 31.544495,
      Zone: 3,
    },
    {
      Column1: "Chipinge",
      Address: "Shop FF6 NSSA Complex Chipinge",
      Latitude: -20.191957,
      Longitude: 32.619516,
    },
    {
      Column1: "Kadoma",
      Address: "Shop M4 Sam Levy Complex Kadoma",
      Latitude: -18.337996,
      Longitude: 29.913446,
      Zone: 4,
    },
    {
      Column1: "Nyanga",
      Address: "02 Shonalanga Nyanga",
      Latitude: -18.232736,
      Longitude: 32.738425,
      Zone: 3,
    },
    {
      Column1: "Rusape",
      Address: "Cnr 13 R Mugabe and H Chitepo Rusape",
      Latitude: -18.532817,
      Longitude: 32.124845,
      Zone: 3,
    },
    {
      Column1: "Zvishavane",
      Address: "1895 R Mugabe Zvishavane",
      Latitude: -20.317373,
      Longitude: 30.054696,
      Zone: 5,
    },
    {
      Column1: "Chegutu",
      Address: "15 Queens Street Chegutu ",
      Latitude: -18.134329,
      Longitude: 30.145975,
      Zone: 4,
    },
    {
      Column1: "Ngezi",
      Address: "95 A MANA COMPLEX NGEZI",
      Latitude: -18.676805,
      Longitude: 30.277845,
    },
    {
      Column1: "Mount Darwin",
      Address: "Number 1, Main St, former FMC Complex, Darwin",
      Latitude: -16.776632,
      Longitude: 31.576467,
      Zone: 4,
    },
    {
      Column1: "Chinhoyi",
      Address: "38B Magamba way Chinhoyi",
      Latitude: -17.359015,
      Longitude: 30.197532,
    },
    {
      Column1: "Murombedzi",
      Address: "stand number 113 Murombedzi",
      Latitude: -17.707498,
      Longitude: 30.200477,
    },
    {
      Column1: "Airport shop",
      Address: "Airport",
      Latitude: -17.918279,
      Longitude: false,
    },
    {
      Column1: "Kariba",
      Address: "SHOP 5 TEE BOX COMPLEX NYAMHUNGA 2  KARIBA ",
      Latitude: -16.517669,
      Longitude: 28.848863,
    },
    {
      Column1: "Longcheng",
      Address: "79/80 Longcheng Complex, Belvedere, Harare",
      Latitude: -17.826585,
      Longitude: 31.003291,
      Zone: 1,
    },
    {
      Column1: "Bindura",
      Address: "Shop 16 NSSA Complex Bindura",
      Latitude: -17.311066,
      Longitude: 31.337641,
    },
    {
      Column1: "Mvurwi",
      Address: "31 Birmingham Road Mvurwi",
      Latitude: -17.03278,
      Longitude: 30.855446,
      Zone: 4,
    },
    {
      Column1: "Harare ShowGrounds",
      Address: "Harare ShowGrounds",
    },
    {
      Column1: "Chimanimani",
      Address: "Stand 263 Zimpost Chimanimani Post Office",
      Latitude: -19.813605,
      Longitude: 32.862725,
    },
    {
      Column1: "Vic falls",
      Address: "shop 21 sawanga mall victoria falls",
      Latitude: -17.930335,
      Longitude: 25.83485,
    },
    {
      Column1: "Eastgate",
      Address: "Eastgate",
      Latitude: -17.83194,
      Longitude: 31.052269,
    },
    {
      Column1: "Kamfinsa",
      Address: "307 Kamfinsa , Greendale Shops, Harare. ",
      Latitude: -17.805579,
      Longitude: 31.12578,
      Zone: 1,
    },

    {
      Column1: "Gwanda Shop",
      Address: "18 NSSA COMPLEX GWANDA",
      Latitude: -20.9440347,
      Longitude: 29.0072282,
      Zone: 2,
    },
    {
      Column1: "Borrowdale shop",
      Address: "Sam Levy's Village SHOP No.28,10 BORROWDALE HARARE",
      Zone: 1,
    },
    {
      Column1: "Mpandawana",
      Address: "Stand No. 385 Zimpost Mpandawana Gutu",
    },
    {
      Column1: "Checheche Zimpost",
      Address: "1020 zimpost Checheche",
      Latitude: -20.761003,
      Longitude: 32.226196,
      Zone: 4,
    },
    {
      Column1: "Shamva",
      Address: "Zimpost Shamva",
      Latitude: -17.298,
      Longitude: 31.5653,
    },
    {
      Column1: "Herbert Chitepo Shop",
      Address: "344 Herbert Chitepo Ave",
      Latitude: -17.8178799,
      Longitude: 31.0629675,
      Zone: 1,
    },
    {
      Column1: "Makoni",
      Address: "Shop no 9 Phillip Munosi Complex Makoni, Chitungwiza",
      Latitude: -18.013076,
      Longitude: 31.103154,
      Zone: 1,
    },
    {
      Column1: "hwange",
      Address: "1 corronation drive hwange",
    },
    {
      Column1: "Vic falls Airport",
      Address: "victoria falls airport",
    },
    {
      Column1: "binga",
      Address: "binga zimpost",
    },
    {
      Column1: "lupane",
      Address: "lupane zimpost",
    },
    {
      Column1: "Mataga",
      Address: "80 Mabhena Complex Mataga",
      Latitude: -20.846341,
      Longitude: 30.194768,
      Zone: 5,
    },
    {
      Column1: "Murambinda",
      Address: "3811 Murambinda Zimpost",
      Latitude: -19.268522,
      Longitude: 31.652995,
    },
    {
      Column1: "Gweru",
      Address: "Corner 7th and R G Mugabe way,CAIPF Building Gweru",
    },
    {
      Column1: "Beitbridge",
      Address: "Shop no 1 zesa complex Beitbridge",
    },

    {
      Column1: "Mutare",
      Address: "93-94 Herbet Chitepo Street, Fidelity Building Mutare",
      Latitude: -18.9733456,
      Longitude: 32.6710023,
      Zone: 3,
    },
    {
      Column1: "Westgate",
      Address: "Shop number 6 Pavilion Westgate",
    },
    {
      Column1: "Magunje",
      Address: "Magunje Zimpost",
      Zone: 4,
    },
    {
      Column1: "Shamva",
      Address: "Shamva Zimpost",
      Zone: 4,
    },
    {
      Column1: "Sanyati.",
      Address: "Sanyati Zimpost",
      Latitude: -17.9869533,
      Longitude: 29.2429117,
      Zone: 4,
    },
  ];

  const [searchTerm, setSearchTerm] = useState("");

  const filteredData = data.filter(
    (item) =>
      item.Column1.includes(searchTerm) || item.Address.includes(searchTerm)
  );

  const renderRow = (item) => {
    return (
      <View
        style={styles.itemContainer}
        bgColor={"white"}
        alignItems={"center"}
        mx={moderateScale(16)}
        my={moderateScale(8)}
        rounded={"xl"}
        shadow={5}>
        <VStack>
          <Text
            fontFamily={"openSansMedium"}
            fontWeight="500"
            color={"gray.600"}
            flex={1}
            fontSize={moderateScale(16)}>
            Shop: {item.Column1}
          </Text>
          <Text
            fontFamily={"openSansMedium"}
            fontWeight="500"
            color={"gray.600"}
            flex={1}
            fontSize={moderateScale(16)}>
            Address: {item.Address}
          </Text>
        </VStack>
      </View>
    );
  };

  return (
    <View
      style={styles.container}
      px={moderateScale(20)}
      py={moderateScale(2)}
      pt={moderateScale(20)}>
      <Center mb={moderateScale(20)}>
        <View className="relative w-full">
          <Input
            className="bg-gray-100 rounded-lg pl-12"
            style={{ height: verticalScale(48) }}>
            <InputField
              placeholder="Search location"
              onChangeText={(text) => setSearchTerm(text)}
              style={{
                fontFamily: "openSansSemiBold",
                fontSize: moderateScale(15),
              }}
            />
          </Input>
          <View className="absolute left-3 top-0 h-full justify-center">
            <Feather name={"search"} size={moderateScale(20)} color="gray" />
          </View>
        </View>
      </Center>

      <FlatList
        data={filteredData}
        renderItem={({ item, index }) => {
          return (
            <HStack
              key={index}
              alignItems={"center"}
              justifyContent={"space-between"}
              p={moderateScale(10)}
              rounded={"md"}>
              <VStack space={moderateScale(10)}>
                <Text
                  fontFamily={"openSansMedium"}
                  fontSize={moderateScale(15)}>
                  {item.Column1}
                </Text>
                <Text fontFamily={"openSans"} fontSize={moderateScale(13)}>
                  {item.Address}
                </Text>
              </VStack>
            </HStack>
          );
        }}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ rowGap: moderateScale(10) }}
        ItemSeparatorComponent={() => {
          return <Divider />;
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    borderRadius: 10,
  },
  itemName: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: "white",
    padding: 10,
  },
  image: {
    width: 30,
    height: 30,
    tintColor: "#F68C1E",
    marginRight: 10,
  },
});

export default Shops;
