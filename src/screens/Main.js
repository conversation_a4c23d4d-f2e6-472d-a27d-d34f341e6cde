import { MaterialCommunityIcons, SimpleLineIcons } from "@expo/vector-icons";
import { Link, useNavigation } from "@react-navigation/native";
import {
  AlertDialog,
  Box,
  Button,
  Center,
  HStack,
  Heading,
  Image,
  ScrollView,
  Spinner,
  Stack,
  StatusBar,
  Text,
  VStack,
  View,
} from "native-base";
import React, { useEffect, useLayoutEffect, useRef, useState } from "react";
import {
  FlatList,
  ImageBackground,
  Linking,
  Platform,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import Toast from "react-native-root-toast";
import { moderateScale, scale, verticalScale } from "react-native-size-matters";
import Card from "../assets/Card.png";
import AppbarMain from "../components/Headers/AppbarMain";
import LoadingModal from "../components/Loading/LoadingModal";
import { greeting } from "../utilities/greeting";
import LocalStore from "../utilities/store";
import Balance from "../services/Balance/Balance";
import ActionSheet from "react-native-actions-sheet";
import SessionTimeOut from "../components/Modals/sessionTimeOut";
import { useSessionStore } from "../utilities/zustandStore";
import { jwtDecode } from "jwt-decode";

const Main4 = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [Phone, setPhone] = useState(null);
  const [init, setInit] = useState("");
  const [failed, setFailed] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const cancelRef = useRef(null);
  const onClose = () => setIsOpen(false);
  const [balance, setBalance] = useState([]);
  const actionSheetRef = useRef(null);
  const [loadingSend, setLoadingSend] = useState(false);
  const [loadingBalance, setLoadingBalance] = useState(false);
  const [usd, setUsd] = useState(0.0);
  const [ZWG, setZWG] = useState(0.0);

  const logout = async () => {
    setLoading(true);
    const store = await LocalStore.deleteData("@userToken");
    await LocalStore.deleteData("@bundles");
    if (store == "success") {
      navigation.navigate("Landing");
      setLoading(false);
    } else {
      setLoading(false);
    }
  };

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [failed]);

  useEffect(() => {
    const getProfile = async () => {
      const Phone = await LocalStore.getData("@username");
      //check if phone number is stored
      if (Phone !== null) {
        setPhone(Phone);
        handleBalanceEnquiry(false, Phone, true);
      }
    };
    getProfile();
  }, []);

  const dataItems = [
    {
      key: "1",
      icon: "currency-usd",
      name: "airtimeBundle",
      displayName: "Buy USD Bundles",
      data: {
        phone: Phone,
      },
    },

    {
      key: "2",
      icon: "credit-card-sync",
      name: "airtimePurchase",
      displayName: "Airtime Recharge",
      data: {
        phone: Phone,
      },
    },

    {
      key: "3",
      icon: "arrow-vertical-lock",
      name: "requestPuk",
      displayName: "Request Puk",
      data: {
        phone: Phone,
      },
    },
    {
      key: "3",
      icon: "dialpad",
      name: "ussdCodes",
      displayName: "USSD Codes",
      data: {
        phone: Phone,
      },
    },
    {
      key: "3",
      icon: "credit-card-chip",
      name: "airtimeBundle",
      displayName: "One Money",
      link: true,
      url: "https://play.google.com/store/apps/details?id=zw.co.onemoney.mob",
      data: {
        phone: Phone,
      },
    },
    {
      key: "3",
      icon: "shopping",
      name: "shops",
      displayName: "Netone Shops",
      data: {
        phone: Phone,
      },
    },
  ];

  useLayoutEffect(() => {
    (async function getBalance() {
      const ZWG = await LocalStore.getData("@ZWG");
      const usd = await LocalStore.getData("@usd");

      if (ZWG !== null) {
        setZWG(ZWG);
      }

      if (usd !== null) {
        setUsd(usd);
      }
    })();
  }, []);

  const handleBalanceEnquiry = async (viewAll, Phone, noloading) => {
    try {
      noloading ? "" : setLoading(true);
      setFailed(false);

      const response = await Balance.getBalance("263" + Phone);
      console.log(response.data);

      if (response.data.success) {
        setLoading(false);

        if (viewAll) {
          const filteredData = [];
          const remainingData = [];

          //combine bundles
          const combinedData = Object.values(
            response.data.body.reduce((acc, item) => {
              if (!acc[item.acctResId]) {
                acc[item.acctResId] = { ...item };
              } else {
                acc[item.acctResId].balance += item.balance;
              }
              return acc;
            }, {})
          );

          combinedData.forEach((item) => {
            if (item.accountResName.includes("Data")) {
              filteredData.push({
                ...item,
                balance: `${Math.abs(item.balance / (1024 * 1024)).toFixed(
                  2
                )} MB`,
              });
            } else if (item.accountResName.includes("Voice")) {
              filteredData.push({
                ...item,
                balance: `${Math.abs(item.balance / 60)} minutes`,
              });
            } else {
              remainingData.push({
                ...item,
                balance: Math.abs(item.balance.toFixed(3)),
              });
            }
          });

          const usd = remainingData.find(
            (item) => item.accountResName === "USD Currency Balance"
          );
          const ZWG = remainingData.find(
            (item) => item.accountResName === "ZWG Currency"
          );
          const others = remainingData.filter(
            (item) =>
              item.accountResName !== "USD Currency Balance" &&
              item.accountResName !== "ZWG Currency"
          );

          setBalance([usd, ZWG, ...filteredData, ...others]);

          setTimeout(() => {
            actionSheetRef.current.show();
          }, 500);
        } else {
          setUsd(0.0);
          LocalStore.saveData("@usd", 0.0);
          response.data.body.forEach((element) => {
            if (element.accountResName === "ZWG Currency") {
              setZWG(element.balance);
              LocalStore.saveData("@ZWG", element.balance);
            }

            if (element.accountResName === "USD Currency Balance") {
              setUsd(element.balance);
              LocalStore.saveData("@usd", element.balance);
            }
          });
        }
      } else {
        setFailed(true);
        setErrorMessage(response.data.message);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setFailed(true);
      console.log(error);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  const GridComponent = () => {
    const renderItem = ({ item }) => (
      <View
        style={{
          flex: 1,
          height: verticalScale(110),
          margin: moderateScale(0),
        }}>
        <VStack alignItems={"center"}>
          <TouchableOpacity
            onPress={async () => {
              if (item.link) {
                await Linking.openURL(item.url);
              } else {
                navigation.navigate(item.name, item.data);
              }
            }}>
            <Box
              alignItems="center"
              borderWidth={1}
              p={moderateScale(10)}
              height={scale(60)}
              width={scale(60)}
              justifyContent={"center"}
              rounded="full"
              overflow="hidden"
              borderColor={"white"}
              bg={"#F68C1E"}>
              <MaterialCommunityIcons
                name={item.icon}
                size={moderateScale(30)}
                color="white"
              />
            </Box>
          </TouchableOpacity>
          <Text
            mt={moderateScale(4)}
            fontFamily={"openSansMedium"}
            color={"#36454F"}
            fontWeight="500"
            textAlign={"center"}
            fontSize={moderateScale(15)}>
            {item.displayName}
          </Text>
        </VStack>
      </View>
    );

    return (
      <FlatList
        data={dataItems}
        renderItem={renderItem}
        contentContainerStyle={{ rowGap: 10 }}
        numColumns={3}
        keyExtractor={(item) => item.key}
      />
    );
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "#F68C1E" }}>
      <StatusBar backgroundColor={"#F68C1E"} />
      {useSessionStore.getState().timeout === true && <SessionTimeOut />}
      <View h={"full"} bg={"gray.100"} w={"full"}>
        <LoadingModal isLoading={loading} />
        <ActionSheet
          ref={actionSheetRef}
          animated={true}
          gestureEnabled={false}
          closeOnTouchBackdrop={loadingSend == true ? false : true}
          isModal={true}>
          {loading ? (
            <Spinner size={"lg"}></Spinner>
          ) : (
            <View
              w="100%"
              h={"80%"}
              px={moderateScale(16)}
              pt={moderateScale(4)}>
              <Center mb={moderateScale(30)}>
                <Text
                  fontFamily={"openSansSemiBold"}
                  mt={moderateScale(20)}
                  fontSize={moderateScale(15)}>
                  Airtime & Bundles Balances List
                </Text>
              </Center>
              <View h={"100%"}>
                <FlatList
                  flex={2}
                  showsVerticalScrollIndicator={false}
                  data={balance}
                  contentContainerStyle={{
                    paddingBottom: moderateScale(100),
                  }}
                  renderItem={({ item, index }) => {
                    return (
                      <HStack
                        key={index}
                        backgroundColor={"gray.100"}
                        rounded={"xl"}
                        w="100%"
                        p={moderateScale(10)}
                        px={moderateScale(20)}
                        justifyContent={"space-between"}>
                        <HStack space={moderateScale(15)}>
                          <VStack space={moderateScale(10)}>
                            <Text
                              fontFamily={"openSansSemiBold"}
                              fontSize={moderateScale(15)}>
                              {item.accountResName}
                            </Text>
                            <Text
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(13)}
                              color={"gray.800"}>
                              balance : {item.balance}
                            </Text>
                          </VStack>
                        </HStack>
                      </HStack>
                    );
                  }}
                  numColumns={1}
                  keyExtractor={(item) => item.accountResName}
                  ItemSeparatorComponent={() => {
                    return <Box m={moderateScale(10)}></Box>;
                  }}
                  ListEmptyComponent={() => {
                    return (
                      <Center>
                        <Text
                          fontFamily={"openSansSemiBold"}
                          mt={moderateScale(100)}
                          fontSize={moderateScale(15)}>
                          No Balances Found
                        </Text>
                      </Center>
                    );
                  }}
                />
              </View>
            </View>
          )}
        </ActionSheet>
        <Box bgColor={"#F68C1E"} w={"full"} px={moderateScale(10)}>
          <View bg={"transparent"}>
            {Platform.OS == "android" ? (
              <Box
                bg={"transparent"}
                px={moderateScale(0)}
                safeArea
                pb={moderateScale(0)}>
                <AppbarMain
                  greeting={greeting()}
                  name={""}
                  phone={Phone}
                  initials={"JT"}
                  logout={logout}
                />
              </Box>
            ) : (
              <Box
                px={moderateScale(0)}
                pb={moderateScale(0)}
                bg={"transparent"}>
                <AppbarMain
                  greeting={greeting()}
                  name={""}
                  phone={Phone}
                  initials={"JT"}
                  logout={logout}
                />
              </Box>
            )}
          </View>
        </Box>
        <View w={"full"} px={moderateScale(10)} py={moderateScale(20)}>
          <Box
            rounded={"2xl"}
            w={"full"}
            shadow={"3"}
            overflow="hidden"
            bg={"white"}
            h={moderateScale(190)}>
            <ImageBackground
              source={Card}
              resizeMode="cover"
              style={{
                justifyContent: "center",
              }}>
              <Stack
                h={moderateScale(135)}
                py={moderateScale(20)}
                space={4}
                justifyContent={"center"}
                alignItems={"center"}>
                <Text
                  fontFamily={"openSansBold"}
                  color={"white"}
                  textAlign={"center"}
                  fontSize={moderateScale(15)}>
                  Airtime Balance
                </Text>
                <VStack space={0}>
                  <HStack space={2}>
                    <Text
                      fontFamily={"openSansSemiBold"}
                      color={"white"}
                      textAlign={"center"}
                      fontSize={moderateScale(12)}>
                      ZWG
                    </Text>

                    <Text
                      fontFamily={"openSansBold"}
                      color={"white"}
                      fontWeight="500"
                      textAlign={"center"}
                      fontSize={moderateScale(25)}>
                      {Math.abs(ZWG).toFixed(1)}
                    </Text>
                  </HStack>
                  <HStack space={2}>
                    <Text
                      fontFamily={"openSansSemiBold"}
                      color={"white"}
                      fontWeight="500"
                      textAlign={"center"}
                      fontSize={moderateScale(12)}>
                      USD
                    </Text>

                    <Text
                      fontFamily={"openSansBold"}
                      color={"white"}
                      fontWeight="500"
                      textAlign={"center"}
                      fontSize={moderateScale(25)}>
                      {Math.abs(usd).toFixed(1)}
                    </Text>
                  </HStack>
                </VStack>
              </Stack>
            </ImageBackground>

            <Box bg={"white"} h={moderateScale(50)} w={"full"}>
              <HStack justifyContent={"center"} pt={moderateScale(2)} space={4}>
                <TouchableOpacity
                  onPress={() => {
                    handleBalanceEnquiry(false, Phone);
                  }}>
                  <View
                    rounded={"full"}
                    justifyContent={"center"}
                    p={moderateScale(8)}
                    bg={"black"}
                    px={moderateScale(15)}
                    h={moderateScale(40)}
                    mt={moderateScale(5)}>
                    <Center>
                      <HStack
                        space={2}
                        alignContent={"center"}
                        alignItems={"center"}>
                        <Text
                          fontFamily={"openSansSemiBold"}
                          fontSize={moderateScale(12)}
                          color={"white"}
                          fontWeight="500">
                          refresh
                        </Text>
                        <SimpleLineIcons
                          name="refresh"
                          size={moderateScale(15)}
                          color="white"
                        />
                      </HStack>
                    </Center>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    handleBalanceEnquiry(true, Phone);
                  }}>
                  <View
                    rounded={"full"}
                    justifyContent={"center"}
                    p={moderateScale(8)}
                    bg={"black"}
                    px={moderateScale(15)}
                    h={moderateScale(40)}
                    mt={moderateScale(5)}>
                    <Center>
                      <HStack
                        space={2}
                        alignContent={"center"}
                        alignItems={"center"}>
                        <Text
                          fontFamily={"openSansSemiBold"}
                          fontSize={moderateScale(12)}
                          color={"white"}
                          fontWeight="500">
                          view more
                        </Text>
                        <SimpleLineIcons
                          name="eye"
                          size={moderateScale(15)}
                          color="white"
                        />
                      </HStack>
                    </Center>
                  </View>
                </TouchableOpacity>
              </HStack>
            </Box>
          </Box>
        </View>

        <View bgColor={"white"} flex={1} w={"full"} pt={moderateScale(15)}>
          <AlertDialog
            leastDestructiveRef={cancelRef}
            isOpen={isOpen}
            onClose={onClose}>
            <AlertDialog.Content p={moderateScale(10)}>
              <AlertDialog.Body>
                <Text
                  fontFamily={"openSansMedium"}
                  color={"#36454F"}
                  fontWeight="500"
                  fontSize={moderateScale(14)}>
                  Do you want to logout ?
                </Text>
              </AlertDialog.Body>
              <HStack justifyContent={"flex-end"}>
                <Button.Group space={moderateScale(24)}>
                  <Button
                    variant="unstyled"
                    colorScheme="coolGray"
                    onPress={onClose}
                    ref={cancelRef}>
                    <Text
                      fontFamily={"openSansMedium"}
                      color={"#36454F"}
                      fontWeight="500"
                      fontSize={moderateScale(14)}>
                      No
                    </Text>
                  </Button>
                  <Button
                    variant="unstyled"
                    colorScheme="coolGray"
                    onPress={logout}>
                    <Text
                      fontFamily={"openSansMedium"}
                      color={"#36454F"}
                      fontWeight="500"
                      fontSize={moderateScale(14)}>
                      Yes
                    </Text>
                  </Button>
                </Button.Group>
              </HStack>
            </AlertDialog.Content>
          </AlertDialog>
          <Toast
            visible={failed}
            position={30}
            shadow={true}
            animation={true}
            hideOnPress={true}
            backgroundColor={"red"}
            opacity={0.9}>
            {errorMessage}
          </Toast>
          <Box px={moderateScale(16)} pt={moderateScale(16)}>
            <HStack
              justifyContent={"space-between"}
              mb={moderateScale(20)}
              alignContent={"center"}
              alignItems={"center"}>
              <HStack space={2} alignItems={"center"}>
                <Text
                  fontFamily={"openSansMedium"}
                  fontWeight="500"
                  fontSize={moderateScale(16)}>
                  Quick Actions
                </Text>
              </HStack>
            </HStack>

            <GridComponent />
          </Box>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 10,
    borderWidth: 1,
    margin: 4,
    borderColor: "#ccc",
    borderRadius: 10,
  },
  itemName: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 0,
  },
});

export default Main4;
