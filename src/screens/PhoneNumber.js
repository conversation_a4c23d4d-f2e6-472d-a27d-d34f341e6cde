import { useNavigation } from "@react-navigation/native";
import { Box } from "@/components/ui/box";
import { Button, ButtonText } from "@/components/ui/button";
import { FormControl } from "@/components/ui/form-control";
import { Heading } from "@/components/ui/heading";
import { Input, InputField } from "@/components/ui/input";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { View } from "@/components/ui/view";
import React, { useEffect, useState } from "react";
import Toast from "react-native-root-toast";
import { moderateScale, scale, verticalScale } from "react-native-size-matters";
import LoadingModal from "../components/Loading/LoadingModal";
import AuthService from "../services/Auth/Auth";

const PhoneNumber = (props) => {
  const navigation = useNavigation();
  const [phone, setPhone] = useState("");
  const [phoneError, setPhoneError] = useState("");
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [failed, setFailed] = useState(false);

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [failed]);

  const checkNumber = (num) => {
    const numberRegexp2 = /^(0712980|712980)/;
    if (numberRegexp2.test(num) === true) {
      return true;
    } else {
      return false;
      /*       const number = parseInt(num)
      return numbers.includes(number) */
    }
  };

  const validate = () => {
    const numberRegexp = /^(071|71)/;

    if (!phone) {
      setPhoneError("This field is required");
      return false;
    }

    if (phone.length < 9) {
      setPhoneError("Invalid phone number, not enough digits");
      return false;
    }

    if (phone.length > 10) {
      setPhoneError("Invalid phone number, too many digits");
      return false;
    }

    if (!numberRegexp.test(phone)) {
      setPhoneError("Invalid phone number, should be a netone number");
      return false;
    }
    return true;
  };

  const sendPhoneNumber = async () => {
    try {
      const res = validate();
      if (!res) {
        return;
      }
      setLoading(true);
      setFailed(false);
      const response = await AuthService.GenerateOtp(parseInt(phone));

      if (response.data.success) {
        navigation.navigate("otp", {
          number: parseInt(phone),
          to: props.route.params.to,
        });
        setLoading(false);
      } else {
        setFailed(true);
        setErrorMessage("Failed to send code");
        setLoading(false);
      }
    } catch (error) {
      setLoading(false);
      setFailed(true);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };
  return (
    <View
      w="100%"
      bg={"white"}
      h={"100%"}
      px={moderateScale(24)}
      pt={moderateScale(24)}>
      <LoadingModal isLoading={loading} />
      <Box safeArea w="100%">
        <Toast
          visible={failed}
          position={30}
          shadow={true}
          animation={true}
          hideOnPress={true}
          backgroundColor={"red"}
          opacity={0.9}>
          {errorMessage}
        </Toast>

        <Heading
          fontSize={moderateScale(24)}
          mt={moderateScale(24)}
          fontFamily={"openSansSemiBold"}
          color="coolGray.800">
          Enter your phone number
        </Heading>
        <Heading
          mt={moderateScale(4)}
          fontFamily={"openSans"}
          color="coolGray.600"
          fontWeight="openSansMedium"
          fontSize={moderateScale(15)}>
          We will send a code (via SMS) to your phone number
        </Heading>

        <VStack className="mt-12" style={{ gap: moderateScale(16) }}>
          <FormControl>
            <View className="relative">
              <Input
                className={`${
                  phoneError ? "border-red-400" : "border-gray-300"
                } bg-gray-100 rounded-lg pl-16`}
                style={{ height: verticalScale(48) }}>
                <InputField
                  placeholder="Netone Mobile Number"
                  onFocus={() => {
                    setPhoneError("");
                  }}
                  onChangeText={(text) => setPhone(text)}
                  keyboardType="number-pad"
                  style={{
                    fontFamily: "openSansSemiBold",
                    fontSize: moderateScale(15),
                  }}
                />
              </Input>
              <View className="absolute left-3 top-0 h-full justify-center">
                <Text
                  style={{
                    fontSize: moderateScale(15),
                    fontFamily: "openSansMedium",
                    color: "#6B7280",
                  }}>
                  +263
                </Text>
              </View>
            </View>
            <Text
              style={{
                color: "#EF4444",
                fontSize: moderateScale(15),
              }}>
              {phoneError}
            </Text>
          </FormControl>

          <Button
            className="bg-orange-500 rounded-lg"
            style={{
              marginTop: moderateScale(24),
              height: verticalScale(48),
            }}
            onPress={() => {
              sendPhoneNumber();
            }}>
            <ButtonText
              style={{
                fontSize: moderateScale(15),
                color: "white",
                fontFamily: "openSansSemiBold",
              }}>
              Continue
            </ButtonText>
          </Button>
        </VStack>
      </Box>
    </View>
  );
};

export default PhoneNumber;
