import { useNavigation } from "@react-navigation/native";
import { Box } from "@/components/ui/box";
import { Button } from "@/components/ui/button";
import { FormControl } from "@/components/ui/form-control";
import { Heading } from "@/components/ui/heading";
import { Pressable } from "@/components/ui/pressable";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { View } from "@/components/ui/view";
import React, { useEffect, useState } from "react";
import { Keyboard, TouchableOpacity } from "react-native";
import Toast from "react-native-root-toast";
import { moderateScale, verticalScale } from "react-native-size-matters";
import LoadingModal from "../components/Loading/LoadingModal";
import OTPInput from "../components/OTP/MaskedOTP";
import AuthService from "../services/Auth/Auth";

const OTP = (props) => {
  const navigation = useNavigation();
  const [otpCode, setOTPCode] = useState("");
  const [isPinReady, setIsPinReady] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState(props.route.params.number);
  const [loading, setLoading] = useState(false);
  const [failed, setFailed] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const maximumCodeLength = 4;

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
      setSuccess(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [failed, success]);

  const handleVerify = async () => {
    try {
      setLoading(true);

      if (props.route.params.to == "forgetPassword") {
        const response = await AuthService.VerifyOtpForgotPassword(
          parseInt(phoneNumber),
          otpCode
        );

        if (response.data.success) {
          if (response.data.body == null) {
            setFailed(true);
            setErrorMessage("You don't have an account, create one");
          } else {
            navigation.navigate("SetForgotPassword", {
              data: response.data.body,
            });
          }
        } else {
          setFailed(true);
          setErrorMessage("Failed to verify otp, Please try again");
          setLoading(false);
        }
      } else {
        const response = await AuthService.VerifyOtp(
          parseInt(phoneNumber),
          otpCode
        );
        if (response.data.success) {
          if (response.data.body == null) {
            navigation.navigate("SignUp", {
              number: phoneNumber,
            });
          } else {
            if (props.route.params.to === "signUp") {
              setFailed(true);
              setErrorMessage("You already have an account with this number");
            } else {
              navigation.navigate("SetForgotPassword", {
                data: response.data.body,
              });
            }
          }
        } else {
          setFailed(true);
          setErrorMessage("Failed to verify otp, Please try again");
          setLoading(false);
        }
      }

      setLoading(false);
    } catch (error) {
      setLoading(false);
      setFailed(true);
      console.log(error);
      if (error.response) {
        if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  const handleResentOtp = async () => {
    try {
      setLoading(true);
      setSuccess(false);
      setFailed(false);
      const response = await AuthService.GenerateOtp(props.route.params.number);
      if (response.data.success) {
        setLoading(false);
        setSuccess(true);
        setSuccessMessage("OTP sent successfully");
      } else {
        setFailed(true);
        setLoading(false);
        setErrorMessage("Failed to send otp, Please try again");
      }
    } catch (error) {
      setLoading(false);
      setFailed(true);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  return (
    <View
      w="100%"
      bg={"white"}
      h={"100%"}
      px={moderateScale(13)}
      pt={moderateScale(4)}>
      <LoadingModal isLoading={loading} />
      <Toast
        visible={failed}
        position={30}
        shadow={true}
        animation={true}
        hideOnPress={true}
        backgroundColor={"red"}
        opacity={0.9}>
        {errorMessage}
      </Toast>
      <Toast
        visible={success}
        position={30}
        shadow={true}
        animation={true}
        hideOnPress={true}
        backgroundColor={"green"}
        opacity={0.9}>
        {successMessage}
      </Toast>
      <Box
        safeArea
        px={moderateScale(10)}
        pt={moderateScale(24)}
        py={moderateScale(8)}
        w="100%">
        <Heading
          fontSize={moderateScale(20)}
          fontFamily={"openSansSemiBold"}
          fontWeight="600"
          color="coolGray.800">
          Enter Verification Code
        </Heading>
        <Heading
          mt={moderateScale(20)}
          fontFamily={"openSansMedium"}
          color="#F68C1E"
          fontWeight="openSansMedium"
          fontSize={moderateScale(15)}>
          Enter the 4-digit code we just texted to your phone number,{" "}
          {phoneNumber}
        </Heading>

        <TouchableOpacity
          onPress={() => {
            navigation.goBack();
          }}>
          <Heading
            mt={moderateScale(8)}
            fontFamily={"openSans"}
            underline
            color="gray.600"
            fontWeight="openSansMedium"
            fontSize={moderateScale(15)}>
            Edit Number
          </Heading>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            handleResentOtp();
          }}>
          <Heading
            fontFamily={"openSans"}
            mt={moderateScale(8)}
            underline
            color="gray.600"
            fontWeight="openSansMedium"
            fontSize={moderateScale(15)}>
            Resend Code
          </Heading>
        </TouchableOpacity>

        <VStack space={3} mt="50">
          <FormControl>
            <Pressable onPress={Keyboard.dismiss}>
              <OTPInput
                code={otpCode}
                setCode={setOTPCode}
                maximumLength={maximumCodeLength}
                setIsPinReady={setIsPinReady}
              />
            </Pressable>
          </FormControl>

          {!isPinReady ? (
            <></>
          ) : (
            <Button
              mt="10"
              onPress={() => {
                handleVerify();
              }}
              h={verticalScale(48)}
              bg="#F68C1E"
              rounded={"lg"}>
              <Text
                fontSize={moderateScale(15)}
                color="white"
                fontFamily={"openSansSemiBold"}>
                Continue
              </Text>
            </Button>
          )}
        </VStack>
      </Box>
    </View>
  );
};

export default OTP;
