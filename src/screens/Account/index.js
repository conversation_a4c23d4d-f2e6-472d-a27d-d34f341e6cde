import { Entypo } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { ScrollView, Text, View } from "native-base";
import React, { useEffect, useState } from "react";
import { FlatList, StyleSheet, TouchableOpacity } from "react-native";
import { moderateScale } from "react-native-size-matters";
import LocalStore from "../../utilities/store";
import LoadingModal from "../../components/Loading/LoadingModal";

const Account = ({ route }) => {
  const navigation = useNavigation();
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    const getProfile = async () => {
      const profile = await LocalStore.getData("@userProfile");

      if (profile !== null) {
        setProfile(profile);
      }
    };
    getProfile();
  }, []);

  const logout = async () => {
    setLoading(true);
    navigation.navigate("Landing");
    setLoading(false);
  };

  const data = [
    /*   { key: 3, name: "Change Password", route: "password", ref: "PASSWORD" }, */
    { key: 4, name: "Netone shops", route: "shops", ref: "SHOP" },
    { key: 5, name: "Contact Us", route: "contact", ref: "CONTACT" },
  ];

  const renderRow = (item) => {
    return (
      <TouchableOpacity
        onPress={() => {
          navigation.navigate(item.route, {
            options: [],
            title: item.name,
            ref: item.ref,
            phone: profile?.phoneNumber,
          });
        }}>
        <View
          style={styles.itemContainer}
          bgColor={"white"}
          alignItems={"center"}
          mx={moderateScale(16)}
          my={moderateScale(8)}
          rounded={"xl"}
          shadow={5}>
          {/*    <Image source={item.image} style={styles.image}/> */}
          <Text
            fontFamily={"openSansMedium"}
            fontWeight="500"
            color={"gray.600"}
            flex={1}
            fontSize={moderateScale(16)}>
            {item.name}
          </Text>
          <Entypo
            name="chevron-small-right"
            size={moderateScale(24)}
            color="gray"
          />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View flex={1} bgColor={"white"}>
      <View style={styles.container} pt={moderateScale(50)}>
        <LoadingModal isLoading={loading} />
        <FlatList
          data={data}
          renderItem={({ item }) => renderRow(item)}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: moderateScale(100) }}
        />

        <TouchableOpacity
          onPress={() => {
            logout();
          }}>
          <View
            style={styles.itemContainer}
            bgColor={"red.500"}
            alignItems={"center"}
            mx={moderateScale(16)}
            my={moderateScale(8)}
            rounded={"xl"}
            shadow={5}>
            {/*    <Image source={item.image} style={styles.image}/> */}
            <Text
              fontFamily={"openSansMedium"}
              fontWeight="500"
              color={"white"}
              flex={1}
              fontSize={moderateScale(16)}>
              Logout
            </Text>
            <Entypo name="log-out" size={moderateScale(24)} color="white" />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    borderRadius: 10,
  },
  itemName: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: "white",
    padding: 10,
  },
  image: {
    width: 30,
    height: 30,
    tintColor: "#F68C1E",
    marginRight: 10,
  },
});

export default Account;
