import {
  Entypo,
  FontAwesome,
  FontAwesome5,
  FontAwesome6,
  AntDesign,
} from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { Divider } from "@/components/ui/divider";
import { HStack } from "@/components/ui/hstack";
import { Link } from "@/components/ui/link";
import { ScrollView } from "@/components/ui/scroll-view";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { View } from "@/components/ui/view";
import React, { useEffect, useState } from "react";
import { FlatList, Linking, StyleSheet, TouchableOpacity } from "react-native";
import { moderateScale, verticalScale } from "react-native-size-matters";
import LocalStore from "../../utilities/store";

const Contact = ({ route }) => {
  const navigation = useNavigation();
  const [profile, setProfile] = useState(null);
  useEffect(() => {
    const getProfile = async () => {
      const profile = await LocalStore.getData("@userProfile");

      if (profile !== null) {
        setProfile(profile);
      }
    };
    getProfile();
  }, []);

  return (
    <View style={styles.container}>
      <ScrollView px={moderateScale(14)} showsVerticalScrollIndicator={false}>
        <Text
          fontFamily={"openSansMedium"}
          color={"gray.600"}
          flex={1}
          mb={moderateScale(15)}
          mt={moderateScale(10)}
          fontSize={moderateScale(17)}>
          If you have any inquiries, get in touch with us.we are happy to help!
        </Text>
        <View style={styles.itemContainer} my={moderateScale(8)}>
          <VStack space={moderateScale(10)}>
            <HStack
              w={"full"}
              space={moderateScale(20)}
              justifyContent={"space-between"}>
              <Entypo name="phone" size={moderateScale(20)} color="gray" />
              <Text
                fontFamily={"openSansMedium"}
                flex={1}
                fontSize={moderateScale(15)}>
                Call Us
              </Text>
            </HStack>
            <VStack space={moderateScale(4)}>
              <Text
                fontFamily={"openSans"}
                flex={1}
                fontSize={moderateScale(15)}
                color={"blue.700"}
                onPress={() => {
                  Linking.openURL("tel:123");
                }}>
                Toll Free: 123
              </Text>
              <Text
                fontFamily={"openSans"}
                flex={1}
                fontSize={moderateScale(15)}
                color={"blue.700"}
                onPress={() => {
                  Linking.openURL("tel:+263712980795");
                }}>
                +263 712 980 795
              </Text>
              <Text
                fontFamily={"openSans"}
                flex={1}
                fontSize={moderateScale(15)}
                color={"blue.700"}
                onPress={() => {
                  Linking.openURL("tel:+263712980898");
                }}>
                +263 712 980 898
              </Text>
              <Text
                fontFamily={"openSans"}
                flex={1}
                fontSize={moderateScale(15)}
                color={"blue.700"}
                onPress={() => {
                  Linking.openURL("tel:+263712980880");
                }}>
                +263 712 980 880
              </Text>
            </VStack>
          </VStack>
        </View>
        <Divider />
        <View style={styles.itemContainer} my={moderateScale(8)}>
          <VStack space={moderateScale(10)}>
            <HStack
              w={"full"}
              space={moderateScale(20)}
              justifyContent={"space-between"}>
              <Entypo
                name="location-pin"
                size={moderateScale(22)}
                color="gray"
              />
              <Text
                fontFamily={"openSansMedium"}
                flex={1}
                fontSize={moderateScale(15)}>
                HQ Address
              </Text>
            </HStack>
            <VStack>
              <Text
                fontFamily={"openSans"}
                flex={1}
                fontSize={moderateScale(15)}>
                Floor Kopje building Harare
              </Text>
              <Text
                fontFamily={"openSans"}
                flex={1}
                fontSize={moderateScale(15)}>
                1 Jason Moyo Avenue
              </Text>
              <Text
                fontFamily={"openSans"}
                flex={1}
                fontSize={moderateScale(15)}>
                P.O BOx CY 579 Causeway
              </Text>
              <Text
                fontFamily={"openSans"}
                flex={1}
                fontSize={moderateScale(15)}>
                Harare Zimbabwe
              </Text>
            </VStack>
          </VStack>
        </View>
        <Divider />
        <View style={styles.itemContainer} my={moderateScale(8)}>
          <VStack space={moderateScale(10)}>
            <HStack
              w={"full"}
              space={moderateScale(20)}
              justifyContent={"space-between"}>
              <Entypo name="email" size={moderateScale(20)} color="gray" />
              <Text
                fontFamily={"openSansMedium"}
                flex={1}
                fontSize={moderateScale(15)}>
                Email Us
              </Text>
            </HStack>
            <VStack>
              <Text
                fontFamily={"openSans"}
                flex={1}
                fontSize={moderateScale(15)}
                color={"blue.700"}
                onPress={() => {
                  Linking.openURL("mailto:<EMAIL>");
                }}>
                <EMAIL>
              </Text>
            </VStack>
          </VStack>
        </View>
        <Divider />
        <View style={styles.itemContainer} my={moderateScale(8)}>
          <VStack space={moderateScale(10)}>
            <HStack
              w={"full"}
              space={moderateScale(20)}
              justifyContent={"space-between"}>
              <AntDesign name="like1" size={moderateScale(20)} color="gray" />
              <Text
                fontFamily={"openSansMedium"}
                flex={1}
                fontSize={moderateScale(15)}>
                Follow Us
              </Text>
            </HStack>
            <HStack space={moderateScale(20)}>
              <Link
                href="https://www.facebook.com/netonezimbabwe"
                alignItems={"center"}
                justifyContent={"center"}
                mt={moderateScale(24)}
                w={verticalScale(35)}
                h={verticalScale(35)}
                bg="blue.500"
                rounded={"full"}>
                <FontAwesome name="facebook" size={24} color="white" />
              </Link>
              <Link
                href="https://www.tiktok.com/@NetOnecellular?lang=en"
                alignItems={"center"}
                justifyContent={"center"}
                mt={moderateScale(24)}
                w={verticalScale(35)}
                h={verticalScale(35)}
                bg="pink.500"
                rounded={"full"}>
                <FontAwesome5 name="tiktok" size={24} color="white" />
              </Link>
              <Link
                href="https://www.instagram.com/netonecellular/?hl=en"
                alignItems={"center"}
                justifyContent={"center"}
                mt={moderateScale(24)}
                w={verticalScale(35)}
                h={verticalScale(35)}
                bg="red.500"
                rounded={"full"}>
                <FontAwesome name="instagram" size={24} color="white" />
              </Link>
              <Link
                href="https://www.linkedin.com/company/NetOne-cellular"
                alignItems={"center"}
                justifyContent={"center"}
                mt={moderateScale(24)}
                w={verticalScale(35)}
                h={verticalScale(35)}
                bg="blue.500"
                rounded={"full"}>
                <FontAwesome name="linkedin" size={24} color="white" />
              </Link>
              <Link
                href="https://x.com/NetOneCellular"
                alignItems={"center"}
                justifyContent={"center"}
                mt={moderateScale(24)}
                w={verticalScale(35)}
                h={verticalScale(35)}
                bg="blue.500"
                rounded={"full"}>
                <FontAwesome6 name="x-twitter" size={24} color="white" />
              </Link>
            </HStack>
          </VStack>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    borderRadius: 10,
  },
  itemName: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: "white",
    padding: 10,
  },
  image: {
    width: 30,
    height: 30,
    tintColor: "#F68C1E",
    marginRight: 10,
  },
});

export default Contact;
