import { Center } from "@/components/ui/center";
import { Divider } from "@/components/ui/divider";
import { FlatList } from "@/components/ui/flat-list";
import { HStack } from "@/components/ui/hstack";
import { Input, InputField } from "@/components/ui/input";
import { Pressable } from "@/components/ui/pressable";
import { ScrollView } from "@/components/ui/scroll-view";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { View } from "@/components/ui/view";
import React, { useState } from "react";
import { moderateScale, verticalScale, scale } from "react-native-size-matters";
import {
  MaterialCommunityIcons,
  SimpleLineIcons,
  Feather,
} from "@expo/vector-icons";
import { Linking, StyleSheet } from "react-native";

// import { Container } from './styles';

const UssdCodes = () => {
  const data = [
    {
      name: "OneMoney menu",
      code: "*111#",
      phone: "*111#",
    },
    {
      name: "NetOne self-care",
      code: "*123#",
      phone: "*123#",
    },
    {
      name: "Out of bundle browsing on or off",
      code: "*130#, option 2 (Block), restart device",
      phone: "*130#",
    },
    {
      name: "Own Number	",
      code: "*120# and select option 2",
      phone: "*120#",
    },
    {
      name: "Activate Incoming Barrings",
      code: "*35*0000#",
      phone: "*35*0000#",
    },
    {
      name: "Activate Outgoing Barrings",
      code: "*33*0000#",
      phone: "*33*0000#",
    },
    {
      name: "Airtime Balance",
      code: "*171#  option Balance Enquiry",
      phone: "*171#",
    },
    {
      name: "ZWL Bundle balance",
      code: "*171#, Balance Enquiry, Selet bundle in question",
      phone: "*171#",
    },
    {
      name: "USD Bundle Purchase ",
      code: "*379#",
      phone: "*379#",
    },
    {
      name: "USD Bundle Balance",
      code: "*379#",
      phone: "*379#",
    },
    {
      name: "Airtime Top Up",
      code: "*133*recharge pin#",
      phone: "*133#",
    },
    {
      name: "Voter registration check",
      code: "*265#",
      phone: "*265#",
    },
  ];

  const [searchTerm, setSearchTerm] = useState("");

  const filteredData = data.filter(
    (item) => item.name.includes(searchTerm) || item.code.includes(searchTerm)
  );

  return (
    <View
      style={styles.container}
      background={"white"}
      px={moderateScale(20)}
      py={moderateScale(2)}
      pt={moderateScale(20)}
      showsVerticalScrollIndicator={false}>
      <Center mb={moderateScale(20)}>
        <View className="relative w-full">
          <Input
            className="bg-gray-100 rounded-lg pl-12"
            style={{ height: verticalScale(48) }}>
            <InputField
              placeholder="Search ussd code | name"
              onChangeText={(text) => setSearchTerm(text)}
              style={{
                fontFamily: "openSansSemiBold",
                fontSize: moderateScale(15),
              }}
            />
          </Input>
          <View className="absolute left-3 top-0 h-full justify-center">
            <Feather name={"search"} size={moderateScale(20)} color="gray" />
          </View>
        </View>
      </Center>

      <FlatList
        contentContainerStyle={{ rowGap: moderateScale(10) }}
        ItemSeparatorComponent={() => {
          return <Divider />;
        }}
        data={filteredData}
        showsVerticalScrollIndicator={false}
        renderItem={({ item, index }) => {
          return (
            <HStack
              alignItems={"center"}
              justifyContent={"space-between"}
              p={moderateScale(10)}
              rounded={"md"}>
              <VStack space={moderateScale(10)}>
                <Text
                  fontFamily={"openSansMedium"}
                  fontSize={moderateScale(15)}>
                  {item.name}
                </Text>
                <Text fontFamily={"openSans"} fontSize={moderateScale(13)}>
                  {item.code}
                </Text>
              </VStack>

              <Pressable
                bg={"gray.100"}
                p={2}
                rounded={"full"}
                onPress={() => {
                  Linking.openURL(`tel:${item.phone}`);
                }}>
                <MaterialCommunityIcons
                  name="phone-forward"
                  size={20}
                  color={"#F68C1E"}
                />
              </Pressable>
            </HStack>
          );
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    borderRadius: 10,
  },
  itemName: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: "white",
    padding: 10,
  },
  image: {
    width: 30,
    height: 30,
    tintColor: "#F68C1E",
    marginRight: 10,
  },
});

export default UssdCodes;
