import { Feather, Ionicons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import {
  Actionsheet,
  Box,
  Button,
  Center,
  FormControl,
  HStack,
  Heading,
  Image,
  Input,
  InputGroup,
  InputLeftAddon,
  InputRightAddon,
  Link,
  Pressable,
  ScrollView,
  Select,
  Text,
  VStack,
  View,
} from "native-base";
import React, { useEffect, useState } from "react";
import { Keyboard, TouchableOpacity } from "react-native";
import Toast from "react-native-root-toast";
import { moderateScale, scale, verticalScale } from "react-native-size-matters";
import netone from "../assets/Icons/netone2.png";
import LoadingModal from "../components/Loading/LoadingModal";
import Auth from "../services/Auth/Auth";
import LocalStore from "../utilities/store";
import { getUniqueId } from "react-native-device-info";
import UserAgent from "react-native-user-agent";
import OTPInput from "../components/OTP/MaskedOTP";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { StatusBar } from "expo-status-bar";
import { useSessionStore } from "../utilities/zustandStore";

const SignIn = () => {
  const navigation = useNavigation();
  const [phone, setPhone] = useState("");
  const [phoneError, setPhoneError] = useState("");
  const [password, setPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [failed, setFailed] = useState(false);
  const [showpassword, setShowPassword] = useState(true);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [pinType, setPinType] = useState("");
  const [PinTypeError, setPinTypeError] = useState("");

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);
    return () => clearInterval(interval);
  }, [failed]);

  useEffect(() => {
    const interval = setTimeout(() => {
      setSuccess(false);
    }, 5000);
    return () => clearInterval(interval);
  }, [success]);

  const validate = () => {
    const numberRegexp = /^(071|71)/;

    if (!phone) {
      setPhoneError("This field is required");
      return false;
    }

    if (phone.length < 9) {
      setPhoneError("Invalid phone number, not enough digits");
      return false;
    }

    if (phone.length > 10) {
      setPhoneError("Invalid phone number, too many digits");
      return false;
    }

    if (!numberRegexp.test(phone)) {
      setPhoneError("Invalid phone number, use a netone number");
      return false;
    }

    if (!pinType) {
      setPinTypeError("This field is required");
      return false;
    }

    if (!password) {
      setPasswordError("This field is required");
      return false;
    }
    return true;
  };

  const handleLogin = async () => {
    try {
      const res = validate();

      if (!res) {
        return;
      }
      setLoading(true);
      setFailed(false);

      const data = {
        msisdn: "263" + parseInt(phone),
        pin: password,
        pinType,
      };

      await LocalStore.saveData("@username", parseInt(phone));
      setPassword("");

      const response = await Auth.login(data);

      if (response.data && response.data.body.token) {
        const data = {
          token: response.data.body.token,
        };
        LocalStore.saveData("@userToken", data);
        setSuccessMessage("You are logged In");
        navigation.navigate("Tabs");
      } else {
        setLoading(false);
        setFailed(true);
        setErrorMessage("Login failed, please try again");
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setFailed(true);

      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (error.response.status == 401) {
          setErrorMessage("Invalid Credentials");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  return (
    <View
      w="100%"
      bg={"white"}
      h={"100%"}
      px={moderateScale(24)}
      pt={moderateScale(0)}>
      <StatusBar style="dark" backgroundColor="#fff" />
      <KeyboardAwareScrollView
        showsVerticalScrollIndicator={false}
        extraScrollHeight={100}>
        <LoadingModal isLoading={loading} />
        <Box safeArea w="100%">
          <Toast
            visible={failed}
            position={30}
            shadow={true}
            animation={true}
            hideOnPress={true}
            backgroundColor={"red"}
            opacity={0.9}>
            {errorMessage}
          </Toast>
          <Toast
            visible={success}
            position={30}
            shadow={true}
            animation={true}
            hideOnPress={true}
            backgroundColor={"green"}
            opacity={0.9}>
            {successMessage}
          </Toast>

          <Center>
            <View p={moderateScale(4)}>
              <Image
                source={netone}
                height={verticalScale(100)}
                width={scale(150)}
                alt={"OneMoney"}
              />
            </View>
            <Heading
              fontSize={moderateScale(20)}
              mt={moderateScale(30)}
              fontFamily={"openSansSemiBold"}
              fontWeight="600"
              color="coolGray.800">
              Welcome!
            </Heading>
          </Center>

          <VStack space={moderateScale(16)} mt={moderateScale(48)}>
            <FormControl>
              <InputGroup>
                <InputLeftAddon
                  isDisabled={phone ? true : false}
                  roundedLeft={"lg"}
                  w={scale(60)}
                  children={
                    <Text
                      fontSize={moderateScale(15)}
                      fontFamily={"openSansMedium"}
                      color={"gray.600"}>
                      {"+263"}
                    </Text>
                  }
                />
                <Input
                  flex={1}
                  defaultValue={`${phone}`}
                  fontFamily={"openSansSemiBold"}
                  onFocus={() => {
                    setPhoneError("");
                  }}
                  focusOutlineColor={"#F68C1E"}
                  borderColor={phoneError ? "red.400" : "gray.300"}
                  placeholder={"Netone Phone Number"}
                  fontSize={moderateScale(15)}
                  bgColor={"gray.100"}
                  rounded={"lg"}
                  onChangeText={(text) => setPhone(text)}
                  h={verticalScale(48)}
                  keyboardType={"number-pad"}
                />
              </InputGroup>
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {phoneError}
              </Text>
            </FormControl>

            <View>
              <Select
                selectedValue={pinType}
                onOpen={() => {
                  setPinTypeError("");
                }}
                bgColor={"gray.100"}
                rounded={"lg"}
                fontSize={moderateScale(15)}
                fontFamily={"openSansMedium"}
                h={verticalScale(48)}
                onFocus={() => {
                  setPinTypeError("");
                }}
                focusOutlineColor={"#F68C1E"}
                borderColor={PinTypeError ? "red.400" : "gray.300"}
                accessibilityLabel="Banks"
                placeholder="Select Pin Type"
                _selectedItem={{
                  bg: "teal.600",
                }}
                onValueChange={(itemValue) => setPinType(itemValue)}>
                <Select.Item label={"Pin 1"} value={"PIN1"} />
                <Select.Item label={"Pin 2"} value={"PIN2"} />
              </Select>
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {PinTypeError}
              </Text>
            </View>

            {pinType && (
              <FormControl>
                <InputGroup>
                  <Input
                    flex={1}
                    defaultValue={`${password}`}
                    secureTextEntry={showpassword}
                    fontFamily={"openSansSemiBold"}
                    onFocus={() => {
                      setPasswordError("");
                    }}
                    focusOutlineColor={"#F68C1E"}
                    borderColor={passwordError ? "red.400" : "gray.300"}
                    placeholder="SimCard Pin"
                    fontSize={moderateScale(15)}
                    bgColor={"gray.100"}
                    rounded={"lg"}
                    onChangeText={(text) => setPassword(text)}
                    h={verticalScale(48)}
                    keyboardType={"default"}
                  />
                  <InputRightAddon
                    roundedRight={"lg"}
                    w={scale(40)}
                    children={
                      <Feather
                        name={showpassword ? "eye-off" : "eye"}
                        size={moderateScale(20)}
                        color="gray"
                        onPress={() => {
                          setShowPassword(!showpassword);
                        }}
                      />
                    }
                  />
                </InputGroup>
              </FormControl>
            )}

            <Button
              mt={moderateScale(24)}
              onPress={() => {
                handleLogin();
              }}
              h={verticalScale(48)}
              bg="#F68C1E"
              rounded={"lg"}>
              <Text
                fontSize={moderateScale(15)}
                color="white"
                fontFamily={"openSansSemiBold"}>
                Log In
              </Text>
            </Button>
          </VStack>
        </Box>
      </KeyboardAwareScrollView>
    </View>
  );
};

export default SignIn;
