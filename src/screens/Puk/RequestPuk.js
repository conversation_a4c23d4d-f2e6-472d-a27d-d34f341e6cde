import { useNavigation } from "@react-navigation/native";
import { MaterialCommunityIcons, Entypo } from "@expo/vector-icons";
import { Box } from "@/components/ui/box";
import { Button } from "@/components/ui/button";
import { FormControl } from "@/components/ui/form-control";
import { HStack } from "@/components/ui/hstack";
import { Input } from "@/components/ui/input";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { View } from "@/components/ui/view";
import { Center } from "@/components/ui/center";
import { FlatList } from "@/components/ui/flat-list";
import { Spinner } from "@/components/ui/spinner";
import { Divider } from "@/components/ui/divider";
import React, { useEffect, useRef, useState } from "react";
import { Dimensions, Pressable, StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { moderateScale, verticalScale } from "react-native-size-matters";
import LoadingModal from "../../components/Loading/LoadingModal";
import ActionSheet from "react-native-actions-sheet";
import Puk from "../../services/SelfCare/Puk";
import LocalStore from "../../utilities/store";
import * as Clipboard from "expo-clipboard";

const RequestPuk = ({ route }) => {
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const height = Dimensions.get("window").height;
  const [failed, setFailed] = useState(false);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const navigation = useNavigation();
  const [nationalId, setNationalID] = useState(null);
  const [nationalIdError, setNationalIDError] = useState("");
  const [idField, setIdField] = useState(false);
  const [lastName, setLastName] = useState();
  const [errorLastName, setErrorLastName] = useState();
  const [details, setDetails] = useState([]);
  const actionSheetRef = useRef(null);
  const [loadingSend, setLoadingSend] = useState(false);
  const [Phone, setPhone] = useState(null);

  useEffect(() => {
    const getPhone = async () => {
      const Phone = await LocalStore.getData("@username");
      //check if phone number is stored
      if (Phone !== null) {
        setPhone(Phone);
      }
    };
    getPhone();
  }, []);

  const validate = () => {
    if (!lastName) {
      setErrorLastName("This field is required");
      return false;
    }
    if (!/^[a-zA-Z]+$/.test(lastName)) {
      setErrorLastName("Last name must contain letters only");
      return false;
    }
    //check national ID
    if (!nationalId) {
      setNationalIDError("This field is required");
      return false;
    }

    if (!idField) {
      setNationalIDError(
        "National ID field is invalid, check for space/s between letters or and the end or make sure its a correct zimbabwean ID."
      );
      return false;
    }

    return true;
  };

  const getDetails = async () => {
    try {
      setLoading(true);
      setFailed(false);
      const response = await Puk.getDetails("263" + Phone);

      if (response.data.success) {
        setLoading(false);
        setDetails([response.data.body]);
      } else {
        setFailed(true);
        setErrorMessage(response.data.message);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setFailed(true);
      console.log(error);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  const handleInputChange = (text) => {
    const regex = /^([0-9][0-9])-([0-9]{6}|[0-9]{7})-([a-zA-Z])([0-9]{2})$/;
    setIdField(regex.test(text));
    setNationalID(text);
  };

  const copyToClipboard = async (text) => {
    await Clipboard.setStringAsync(text);
  };

  return (
    <SafeAreaView>
      <View w="100%" bg={"white"} h={height}>
        <VStack flex={1} px={moderateScale(20)} w="100%">
          <VStack space={moderateScale(5)} mt={moderateScale(30)}>
            <LoadingModal isLoading={loading} />

            <Button
              onPress={() => {
                getDetails();
              }}
              h={verticalScale(48)}
              bg="#F68C1E"
              rounded={"lg"}>
              <HStack space={moderateScale(10)} alignItems={"center"}>
                <Text
                  fontSize={moderateScale(15)}
                  color="white"
                  fontFamily={"openSansSemiBold"}>
                  Get Puk Details
                </Text>
              </HStack>
            </Button>
            {details ? (
              details.map((item, index) => {
                return (
                  <View key={index} pt={moderateScale(4)}>
                    <Center mb={moderateScale(30)}>
                      <Text
                        fontFamily={"openSansSemiBold"}
                        mt={moderateScale(20)}
                        fontSize={moderateScale(15)}>
                        Your Puk Details:
                      </Text>
                    </Center>
                    <HStack
                      backgroundColor={"gray.100"}
                      rounded={"xl"}
                      w="100%"
                      p={moderateScale(10)}
                      px={moderateScale(20)}
                      justifyContent={"space-between"}>
                      <HStack space={moderateScale(15)} w={"full"}>
                        <VStack space={moderateScale(20)} w={"full"}>
                          <HStack space={moderateScale(80)}>
                            <Text
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(13)}
                              color={"gray.800"}>
                              Puk1 : {item.puk1}
                            </Text>
                            <Pressable
                              onPress={() => {
                                copyToClipboard(item.puk1);
                              }}>
                              <MaterialCommunityIcons
                                name="content-copy"
                                color={"gray"}
                                size={20}
                              />
                            </Pressable>
                          </HStack>
                          <Divider w={"full"} />

                          <HStack space={moderateScale(80)}>
                            <Text
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(13)}
                              color={"gray.800"}>
                              Puk2 : {item.puk2}
                            </Text>
                            <Pressable
                              onPress={() => {
                                copyToClipboard(item.puk2);
                              }}>
                              <MaterialCommunityIcons
                                name="content-copy"
                                color={"gray"}
                                size={20}
                              />
                            </Pressable>
                          </HStack>
                        </VStack>
                      </HStack>
                    </HStack>
                  </View>
                );
              })
            ) : (
              <Center>
                <Text
                  fontFamily={"openSansSemiBold"}
                  mt={moderateScale(100)}
                  fontSize={moderateScale(15)}>
                  No Puk Details
                </Text>
              </Center>
            )}
          </VStack>
        </VStack>
        {/*       <ActionSheet
          ref={actionSheetRef}
          animated={true}
          gestureEnabled={loadingSend == true ? false : true}
          closeOnTouchBackdrop={loadingSend == true ? false : true}
          isModal={true}>
          {loading ? (
            <Spinner size={"lg"}></Spinner>
          ) : (
            <View
              w="100%"
              h={"30%"}
              px={moderateScale(16)}
              pt={moderateScale(4)}>
              <Center mb={moderateScale(30)}>
                <Text
                  fontFamily={"openSansSemiBold"}
                  mt={moderateScale(20)}
                  fontSize={moderateScale(15)}>
                  Puk Details
                </Text>
              </Center>
              <View h={"100%"}>
                <FlatList
                  flex={2}
                  showsVerticalScrollIndicator={false}
                  data={details}
                  renderItem={({ item, index }) => {
                    return (
                     
                    );
                  }}
                  numColumns={1}
                  keyExtractor={(item) => item.accountResName}
                  ItemSeparatorComponent={() => {
                    return <Box m={moderateScale(10)}></Box>;
                  }}
                  ListEmptyComponent={() => {
                    return (
                      <Center>
                        <Text
                          fontFamily={"openSansSemiBold"}
                          mt={moderateScale(100)}
                          fontSize={moderateScale(15)}>
                          No Puk Details Found
                        </Text>
                      </Center>
                    );
                  }}
                />
              </View>
            </View>
          )}
        </ActionSheet> */}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    padding: 20,
  },
  label: {
    fontSize: 16,
    marginTop: 20,
  },
  input: {
    width: "100%",
    height: 40,
    borderColor: "gray",
    borderWidth: 1,
    marginTop: 10,
    padding: 10,
  },
  button: {
    marginTop: 20,
    backgroundColor: "blue",
    padding: 10,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
  },
  errorMessage: {
    marginTop: 20,
    color: "red",
    fontSize: 16,
  },
});

export default RequestPuk;
