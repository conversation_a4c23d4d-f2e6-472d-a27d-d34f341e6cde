import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ather } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { Badge } from "@/components/ui/badge";
import { Box } from "@/components/ui/box";
import { Button, ButtonText } from "@/components/ui/button";
import { Center } from "@/components/ui/center";
import { FlatList } from "@/components/ui/flat-list";
import {
  FormControl,
  FormControlError,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
} from "@/components/ui/form-control";
import { HStack } from "@/components/ui/hstack";
import { Input } from "@/components/ui/input";
import { ScrollView } from "@/components/ui/scroll-view";
import {
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
} from "@/components/ui/select";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { View } from "@/components/ui/view";
import React, { useEffect, useRef, useState } from "react";
import {
  Dimensions,
  PermissionsAndroid,
  Platform,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import ActionSheet from "react-native-actions-sheet";
import * as Cont from "expo-contacts";
import Toast from "react-native-root-toast";
import { moderateScale, scale, verticalScale } from "react-native-size-matters";
import LoadingModal from "../../components/Loading/LoadingModal";
import GetContacts from "../../components/Modals/contacts";
import { FlashList } from "@shopify/flash-list";
import airtimeBundles from "../../services/Bundles/Bundles";
import CurrencyInputField from "../../components/CurrencyInput";
import * as Animatable from "react-native-animatable";

const CreditTransfer = ({ route }) => {
  const [amount, setAmount] = useState();
  const [errorAmount, setErrorAmount] = useState();
  const [number, setNumber] = useState("");
  const [currency, setCurrency] = useState();
  const [errorCurrency, setErrorCurrency] = useState();
  const [errorNumber, setErrorNumber] = useState();
  const [loading, setLoading] = useState(false);
  const [failed, setFailed] = useState(false);
  const height = Dimensions.get("window").height;
  const navigation = useNavigation();
  const [isYesSelected, setIsYesSelected] = useState(true);
  const [isNoSelected, setIsNoSelected] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [openContacts, setOpenContacts] = useState(false);
  const [contacts, setContacts] = useState([]);
  const [searchText, setSearchText] = useState("");
  const [loadingContacts, setLoadingContacts] = useState(false);
  const [errorBundle, setErrorBundle] = useState(false);
  const [bundles, setBundles] = useState([]);
  const [BundleType, setBundleType] = useState("");
  const [BundleTypeError, setBundleTypeError] = useState("");
  const [selectedBundle, setSelectedBundle] = useState();
  const [bundleClasses, setBundleClasses] = useState([]);

  const actionSheetRef = useRef(null);
  const actionSheetRef2 = useRef(null);

  const validate = () => {
    const numberRegexp = /^(071|71)/;

    //check mobile number
    if (!number && isNoSelected) {
      setErrorNumber("This field is required");
      return false;
    }

    if (!numberRegexp.test(number) && isNoSelected) {
      setErrorNumber("Invalid phone number, should be a netone number");
      return false;
    }

    if (number.length < 9 && isNoSelected) {
      setErrorNumber("Invalid phone number, not enough digits");
      return false;
    }

    if (number.length > 10 && isNoSelected) {
      setErrorNumber("Invalid phone number, too many digits");
      return false;
    }

    //check currency
    if (!currency) {
      setErrorCurrency("This field is required");
      return false;
    }

    //check amount
    if (!amount) {
      setErrorAmount("This field is required");
      return false;
    }

    if (amount < 1) {
      setErrorAmount("Amount must be greater than 1");
      return false;
    }

    return true;
  };

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [failed]);

  const handleYesPress = () => {
    setIsYesSelected(true);
    setIsNoSelected(false);
  };

  const handleNoPress = () => {
    setIsNoSelected(true);
    setIsYesSelected(false);
  };

  const handleLookUp = () => {
    const res = validate();
    if (!res) {
      return;
    }

    const allData = {
      buyerPhoneNumber: route.params.phone,
      receiverPhoneNumber: "263" + parseInt(number),
      amount: amount,
      other: isYesSelected,
    };

    navigation.navigate("confirmCreditTransfer", {
      data: allData,
    });
  };

  const filterData = (data) => {
    return data.filter((item) =>
      item?.name?.toLowerCase().includes(searchText.toLowerCase())
    );
  };

  useEffect(() => {
    (async () => {
      const { status } = await Cont.requestPermissionsAsync();
      if (status === "granted") {
        const { data } = await Cont.getContactsAsync();

        if (data.length > 0) {
          setContacts(data);
        }
      }
    })();
  }, []);

  return (
    <SafeAreaView>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View w="100%" bg={"white"} h={height}>
          <Box px={moderateScale(20)} py={moderateScale(10)} w="100%">
            <LoadingModal isLoading={loading} />

            <Toast
              visible={failed}
              position={70}
              shadow={true}
              animation={true}
              hideOnPress={true}
              backgroundColor={"red"}
              opacity={0.9}
              duration={Toast.durations.LONG}>
              {errorMessage}
            </Toast>

            <VStack space={moderateScale(16)} mt={moderateScale(48)}>
              <VStack space={4}>
                <FormControl>
                  <InputGroup>
                    <InputLeftAddon
                      roundedLeft={"lg"}
                      w={scale(60)}
                      children={
                        <Text
                          fontSize={moderateScale(15)}
                          fontFamily={"openSansMedium"}
                          color={"gray.600"}>
                          {"+263"}
                        </Text>
                      }
                    />
                    <Input
                      flex={1}
                      defaultValue={`${number}`}
                      fontFamily={"openSansSemiBold"}
                      onFocus={() => {
                        setErrorNumber("");
                      }}
                      focusOutlineColor={"#F68C1E"}
                      borderColor={errorNumber ? "red.400" : "gray.300"}
                      placeholder="Receivers Netone  Number"
                      fontSize={moderateScale(15)}
                      bgColor={"gray.100"}
                      rounded={"lg"}
                      onChangeText={(text) => setNumber(text)}
                      h={verticalScale(48)}
                      keyboardType={"number-pad"}
                    />
                  </InputGroup>
                  <TouchableOpacity
                    onPress={() => {
                      actionSheetRef.current?.show();
                    }}>
                    <Text color={"blue.400"} fontSize={moderateScale(15)}>
                      Select From Contacts
                    </Text>
                  </TouchableOpacity>
                  <Text color={"red.400"} fontSize={moderateScale(15)}>
                    {errorNumber}
                  </Text>
                </FormControl>
                <FormControl>
                  <FormControlLabel>
                    <FormControlLabelText className="text-base font-medium text-gray-700 mb-2">
                      Currency
                    </FormControlLabelText>
                  </FormControlLabel>
                  <Select
                    selectedValue={currency}
                    onValueChange={(itemValue) => setCurrency(itemValue)}>
                    <SelectTrigger
                      variant="outline"
                      size="md"
                      className={`${
                        errorCurrency ? "border-red-400" : "border-gray-300"
                      } h-12 bg-gray-100`}>
                      <SelectInput
                        placeholder="Currency"
                        className="text-base"
                      />
                      <SelectIcon className="mr-3" />
                    </SelectTrigger>
                    <SelectPortal>
                      <SelectBackdrop />
                      <SelectContent>
                        <SelectDragIndicatorWrapper>
                          <SelectDragIndicator />
                        </SelectDragIndicatorWrapper>
                        <SelectItem label="ZiG" value="ZiG" />
                        {/*   <SelectItem label="USD" value="USD" />  */}
                      </SelectContent>
                    </SelectPortal>
                  </Select>
                  <FormControlError>
                    <FormControlErrorText className="text-red-500 text-sm mt-1">
                      {errorCurrency}
                    </FormControlErrorText>
                  </FormControlError>
                </FormControl>
                <CurrencyInputField
                  decimalPlaces={2}
                  onChange={(value) => {
                    setAmount(value);
                    setErrorAmount("");
                  }}
                  error={errorAmount}
                />
              </VStack>

              <Button
                onPressIn={() => {}}
                mt={moderateScale(24)}
                onPress={() => handleLookUp()}
                h={verticalScale(48)}
                bg="#F68C1E"
                rounded={"lg"}>
                <Text
                  fontSize={moderateScale(15)}
                  color="white"
                  fontFamily={"openSansSemiBold"}>
                  Continue
                </Text>
              </Button>

              {openContacts && (
                <GetContacts
                  open={openContacts}
                  setNumber={(value) => {
                    setNumber(value);
                  }}
                  close={() => {
                    setOpenContacts(false);
                  }}></GetContacts>
              )}

              <ActionSheet
                ref={actionSheetRef}
                containerStyle={{
                  borderTopLeftRadius: 25,
                  borderTopRightRadius: 25,
                }}
                indicatorStyle={{
                  width: 100,
                }}
                gestureEnabled={false}>
                <HStack p={moderateScale(10)}>
                  <TouchableOpacity
                    onPress={() => {
                      actionSheetRef.current?.hide();
                    }}>
                    <Feather name="x" size={moderateScale(24)} color="gray" />
                  </TouchableOpacity>
                </HStack>
                <View
                  w="100%"
                  h={"88%"}
                  px={moderateScale(16)}
                  pt={moderateScale(4)}>
                  <Input
                    fontFamily={"openSansSemiBold"}
                    my={moderateScale(20)}
                    value={searchText}
                    focusOutlineColor={"#F68C1E"}
                    borderColor={"gray.300"}
                    placeholder="Search Contact"
                    fontSize={moderateScale(15)}
                    bgColor={"gray.100"}
                    rounded={"lg"}
                    onChangeText={(text) => setSearchText(text)}
                    h={verticalScale(48)}
                  />
                  {loadingContacts && (
                    <Center>
                      <Text color={"blue.500"}>Getting Contacts ...</Text>
                    </Center>
                  )}
                  {filterData(contacts).length == 0 && !loadingContacts && (
                    <Center>
                      <Text
                        color={"red.500"}
                        fontFamily={"openSansMedium"}
                        fontWeight="500"
                        fontSize={moderateScale(16)}
                        my={moderateScale(15)}>
                        No Contacts Found
                      </Text>
                    </Center>
                  )}
                  {filterData(contacts).length > 0 && !loadingContacts && (
                    <Text
                      color={"gray.500"}
                      fontFamily={"openSansMedium"}
                      fontWeight="500"
                      fontSize={moderateScale(16)}
                      my={moderateScale(15)}>
                      Tap on a contact to select
                    </Text>
                  )}
                  {filterData(contacts).length > 0 && !loadingContacts && (
                    <Animatable.View
                      easing="ease-out"
                      animation={"fadeInUp"}
                      delay={300}>
                      <View height={"100%"} width={"100%"}>
                        <FlashList
                          data={filterData(contacts)}
                          estimatedItemSize={200}
                          showsVerticalScrollIndicator={false}
                          contentContainerStyle={{
                            paddingBottom: moderateScale(200),
                          }}
                          renderItem={({ item }) => {
                            if (
                              item.contactType === "person" &&
                              item.phoneNumbers
                            ) {
                              return (
                                <View key={item} h={verticalScale(70)}>
                                  <View
                                    style={styles.MainContainer}
                                    bgColor={"white"}
                                    mb={moderateScale(15)}
                                    rounded={"xl"}
                                    shadow={0.5}
                                    borderColor={"gray.200"}
                                    borderWidth={1}>
                                    <TouchableOpacity
                                      onPress={() => {
                                        setNumber(
                                          item.phoneNumbers?.[0].number
                                            .toString()
                                            .replace(/\s/g, "")
                                            .replace("+263", "")
                                        );
                                        actionSheetRef.current?.hide();
                                      }}>
                                      <Center h={"100%"}>
                                        <Text style={{ fontSize: 18 }}>
                                          {" "}
                                          {`${item && item?.name}`} -{" "}
                                          {`${
                                            item &&
                                            item?.phoneNumbers[0]?.number
                                          }`}
                                        </Text>
                                      </Center>
                                    </TouchableOpacity>
                                  </View>
                                </View>
                              );
                            }
                          }}
                          numColumns={1}
                        />
                      </View>
                    </Animatable.View>
                  )}
                </View>
              </ActionSheet>
            </VStack>
          </Box>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  MainContainer: {
    flex: 1,
    paddingTop: Platform.OS === "ios" ? 20 : 0,
  },

  title: {
    padding: 12,
    fontSize: 22,
    backgroundColor: "#33691E",
    color: "white",
  },

  contactTitle: {
    fontSize: 22,
    paddingTop: 8,
    paddingBottom: 8,
    paddingLeft: 15,
    color: "black",
  },

  row: {
    flexDirection: "row",
    height: 60,
  },

  avatarContainer: {
    marginLeft: 12,
    justifyContent: "center",
    alignItems: "center",
  },

  listTextContainer: {
    marginLeft: 15,
    flexDirection: "row",
    flex: 1,
  },
});

export default CreditTransfer;
