import { MaterialCommunityIcons, Entypo } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { Box } from "@/components/ui/box";
import { Button } from "@/components/ui/button";
import { Center } from "@/components/ui/center";
import { FlatList } from "@/components/ui/flat-list";
import { FormControl } from "@/components/ui/form-control";
import { HStack } from "@/components/ui/hstack";
import { Input } from "@/components/ui/input";
import { Select } from "@/components/ui/select";
import { Spinner } from "@/components/ui/spinner";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { View } from "@/components/ui/view";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { StyleSheet, TouchableOpacity } from "react-native";
import Toast from "react-native-root-toast";
import { moderateScale, verticalScale } from "react-native-size-matters";
import LoadingModal from "../../components/Loading/LoadingModal";
import LocalStore from "../../utilities/store";
import Numbers from "../../services/Numbers/Numbers";
import ActionSheet from "react-native-actions-sheet";
import { SafeAreaView } from "react-native-safe-area-context";

const LinkedNumbers = ({ route }) => {
  const navigation = useNavigation();
  const [searchTerm, setSearchTerm] = useState("");
  const [searchTermError, setSearchTermError] = useState("");
  const [search, setSearch] = useState("");
  const [searchError, setSearchError] = useState("");
  const [loading, setLoading] = useState(false);
  const [loadingSend, setLoadingSend] = useState(false);
  const [failed, setFailed] = useState(false);
  const [success, setSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [nationalId, setNationalID] = useState(null);
  const [nationalIdError, setNationalIDError] = useState("");
  const [idField, setIdField] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState("");
  const [phone, setPhone] = useState("");
  const [phoneError, setPhoneError] = useState("");
  const [linkedNumbersList, setLinkedNumbersList] = useState([]);

  const actionSheetRef = useRef(null);

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [failed]);

  useEffect(() => {
    const interval = setTimeout(() => {
      setSuccess(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [success]);

  /*  useEffect(() => {
    const getNumbers = async () => {
      const index = LocalStore.getData("@phoneNumber");
      if (index !== null) {
        setPhoneNumber(index);
      }
    };
    getNumbers();
  });
 */
  useEffect(() => {
    // getNumbers();
  }, []);

  const getNumbers = useCallback(async () => {
    try {
      setLoading(true);
      setFailed(false);
      console.log("hjk");
      const response = await Numbers.getNumbers("0712980170");
      console.log(response.data);
      if (response.data.responseCode == "200") {
        setLinkedNumbersList(response.data.data.linkedNumbers);
        setLoading(false);
      } else {
        setFailed(true);
        setErrorMessage("Failed to fetch linked numbers");
        setLoading(false);
      }
    } catch (error) {
      console.log(error);
      setLoading(false);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          setErrorMessage(error.response.data.message);
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
      return false;
    }
  }, []);

  const validate = () => {
    const numberRegexp = /^(071|71)/;

    if (!phone) {
      setPhoneError("This field is required");
      return false;
    }

    if (phone.length < 9) {
      setPhoneError("Invalid phone number, not enough digits");
      return false;
    }

    if (phone.length > 10) {
      setPhoneError("Invalid phone number, too many digits");
      return false;
    }
    /* 
    if (!numberRegexp.test(phone)) {
      setPhoneError("Invalid phone number, should be a netone number");
      return false;
    }
 */
    return true;
  };

  const addPhoneNumber = async () => {
    try {
      const res = validate();
      console.log(res);
      if (!res) {
        return;
      }

      setLoadingSend(true);
      setFailed(false);

      const response = await Numbers.add(parseInt(phone));

      if (response.data.responseCode === "200") {
        setSuccess(true);
        setSuccessMessage("Linked number successfully");
        setLoadingSend(false);
        actionSheetRef.current.hide();
        getNumbers();
      } else {
        setFailed(true);
        setErrorMessage("Failed to link number ");
        setLoadingSend(false);
      }

      setPhoneError("");
    } catch (error) {
      setLoadingSend(false);
      setFailed(true);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          setErrorMessage(error.response.data.message);
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
      return false;
    }
  };

  const handleInputChange = (text) => {
    const regex = /^([0-9][0-9])-([0-9]{6}|[0-9]{7})([a-zA-Z])([0-9]{2})$/;
    setIdField(regex.test(text));
    setNationalID(text);
  };

  return (
    <SafeAreaView>
      <View
        background={"white"}
        px={moderateScale(20)}
        py={moderateScale(2)}
        pt={moderateScale(10)}
        h={"100%"}>
        <LoadingModal isLoading={loading} />

        <Toast
          visible={failed}
          position={30}
          shadow={true}
          animation={true}
          hideOnPress={true}
          backgroundColor={"red"}
          opacity={0.9}>
          {errorMessage}
        </Toast>
        <Toast
          visible={success}
          position={30}
          shadow={true}
          animation={true}
          duration={5000}
          hideOnPress={true}
          backgroundColor={"green"}
          opacity={0.9}>
          {successMessage}
        </Toast>
        <VStack flex={1}>
          <VStack space={moderateScale(5)} mt={moderateScale(30)}>
            <Button
              onPress={() => {
                actionSheetRef.current?.show();
              }}
              h={verticalScale(48)}
              bg="#F68C1E"
              rounded={"lg"}>
              <HStack space={moderateScale(10)} alignItems={"center"}>
                <Text
                  fontSize={moderateScale(15)}
                  color="white"
                  fontFamily={"openSansSemiBold"}>
                  Add Number
                </Text>
                <MaterialCommunityIcons
                  name="plus"
                  size={moderateScale(30)}
                  color="white"
                />
              </HStack>
            </Button>
          </VStack>

          <FlatList
            flex={2}
            showsVerticalScrollIndicator={false}
            data={linkedNumbersList}
            renderItem={({ item, index }) => {
              return (
                <TouchableOpacity
                  key={item.phoneNumber}
                  onPress={() => {
                    navigation.navigate("CustomerDetails", { customer: item });
                  }}>
                  <HStack
                    backgroundColor={"gray.100"}
                    rounded={"xl"}
                    w="100%"
                    p={moderateScale(10)}
                    px={moderateScale(20)}
                    justifyContent={"space-between"}>
                    <HStack space={moderateScale(15)}>
                      <VStack space={moderateScale(5)}>
                        <Text
                          fontFamily={"openSansSemiBold"}
                          fontSize={moderateScale(15)}>
                          kkkkkkllllllll
                        </Text>
                        <Text
                          fontFamily={"openSansMedium"}
                          fontSize={moderateScale(13)}
                          color={"gray.500"}>
                          Phone Number :
                        </Text>
                      </VStack>
                    </HStack>
                  </HStack>
                </TouchableOpacity>
              );
            }}
            numColumns={1}
            keyExtractor={(item) => item.phoneNumber}
            ItemSeparatorComponent={() => {
              return <Box m={moderateScale(10)}></Box>;
            }}
            ListEmptyComponent={() => {
              return (
                <Center>
                  <Text
                    fontFamily={"openSansSemiBold"}
                    mt={moderateScale(100)}
                    fontSize={moderateScale(15)}>
                    No Linked Numbers Found
                  </Text>
                </Center>
              );
            }}
          />
        </VStack>
        <ActionSheet
          ref={actionSheetRef}
          animated={true}
          gestureEnabled={loadingSend == true ? false : true}
          closeOnTouchBackdrop={loadingSend == true ? false : true}
          isModal={true}>
          <View w="100%" h={"50%"} px={moderateScale(16)} pt={moderateScale(4)}>
            <Center mb={moderateScale(30)}>
              <Text
                fontFamily={"openSansSemiBold"}
                mt={moderateScale(20)}
                fontSize={moderateScale(15)}>
                Add Number
              </Text>
            </Center>
            <Input
              fontFamily={"openSansSemiBold"}
              onFocus={() => {
                setPhoneError("");
              }}
              onTextInput={() => {
                setPhoneError("");
              }}
              focusOutlineColor={"#F68C1E"}
              borderColor={phoneError ? "red.400" : "gray.300"}
              placeholder="Phone Number"
              numberOfLines={4}
              fontSize={moderateScale(15)}
              bgColor={"gray.100"}
              rounded={"lg"}
              onChangeText={(text) => setPhone(text)}
              h={verticalScale(48)}
              mb={moderateScale(2)}
            />

            <Text color={"red.400"} fontSize={moderateScale(15)}>
              {phoneError}
            </Text>
            <Button
              onPress={() => {
                //addPhoneNumber();
              }}
              h={verticalScale(48)}
              mt={moderateScale(20)}
              bg="#F68C1E"
              rounded={"lg"}>
              <HStack space={moderateScale(10)} alignItems={"center"}>
                {loadingSend ? (
                  <Spinner color={"white"} size={"lg"}></Spinner>
                ) : (
                  <Text
                    fontSize={moderateScale(15)}
                    color="white"
                    fontFamily={"openSansSemiBold"}>
                    Add
                  </Text>
                )}
              </HStack>
            </Button>
          </View>
        </ActionSheet>
      </View>
    </SafeAreaView>
  );
};

const customPickerStyles = StyleSheet.create({
  inputIOS: {
    fontSize: 14,
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: "green",
    borderRadius: 8,
    color: "black",
    paddingRight: 30, // to ensure the text is never behind the icon
  },
  inputAndroid: {
    fontSize: 14,
    paddingHorizontal: 10,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: "blue",
    borderRadius: 8,
    color: "black",
    paddingRight: 30, // to ensure the text is never behind the icon
  },
});

export default LinkedNumbers;
