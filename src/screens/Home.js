import { Box } from "@/components/ui/box";
import { HStack } from "@/components/ui/hstack";
import { ScrollView } from "@/components/ui/scroll-view";
import { View } from "@/components/ui/view";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { Divider } from "@/components/ui/divider";
import { Button } from "@/components/ui/button";
import React from "react";
import { SafeAreaView } from "react-native";
import { moderateScale } from "react-native-size-matters";
import {
  Octicons,
  Ionicons,
  FontAwesome5,
  MaterialCommunityIcons,
} from "@expo/vector-icons";

const Home = () => {
  return (
    <View h={"full"} bg={"amber.300"} w={"full"}>
      <Box
        bgColor={"#CF7500"}
        w={"full"}
        h={moderateScale(100)}
        p={moderateScale(20)}>
        <Button>
          <Text color={"white"}>Enquire Balance</Text>
        </Button>
      </Box>
      <View bgColor={"#F0A500"} flex={1} w={"full"} p={moderateScale(20)}>
        <HStack justifyContent={"space-around"} flex={1}>
          <VStack
            alignItems={"center"}
            justifyContent={"center"}
            space={moderateScale(10)}
            flex={1}
            h={"full"}>
            <FontAwesome5
              name="sim-card"
              size={moderateScale(30)}
              color="white"
            />
            <Text color={"white"}>Reset Puk</Text>
          </VStack>
          <Divider
            bg={"gray.300"}
            thickness={"0.5"}
            orientation="vertical"></Divider>

          <VStack
            h={"full"}
            flex={1}
            justifyContent={"center"}
            alignItems={"center"}
            space={moderateScale(10)}>
            <FontAwesome5 name="link" size={moderateScale(30)} color="white" />
            <Text color={"white"}>Link Numbers</Text>
          </VStack>
        </HStack>

        <Divider bg={"gray.300"} thickness={"0.5"}></Divider>
        <HStack justifyContent={"space-around"} flex={1}>
          <VStack
            justifyContent={"center"}
            alignItems={"center"}
            space={moderateScale(10)}
            flex={1}
            h={"full"}>
            <FontAwesome5 name="lock" size={moderateScale(30)} color="white" />
            <Text color={"white"}>Change Password</Text>
          </VStack>
          <Divider
            bg={"gray.300"}
            thickness={"0.5"}
            orientation="vertical"></Divider>

          <VStack
            justifyContent={"center"}
            h={"full"}
            flex={1}
            alignItems={"center"}
            space={moderateScale(10)}>
            <MaterialCommunityIcons
              name="form-textbox-password"
              size={moderateScale(30)}
              color="white"
            />
            <Text color={"white"}>Change Passphrase</Text>
          </VStack>
        </HStack>
        <Divider bg={"gray.300"} thickness={"0.5"}></Divider>
        <HStack justifyContent={"space-around"} flex={1}>
          <VStack
            justifyContent={"center"}
            alignItems={"center"}
            space={moderateScale(10)}
            flex={1}
            h={"full"}>
            <FontAwesome5
              name="user-alt"
              size={moderateScale(30)}
              color="white"
            />
            <Text color={"white"}>My Profile</Text>
          </VStack>
          <Divider
            bg={"gray.300"}
            thickness={"0.5"}
            orientation="vertical"></Divider>

          <VStack
            justifyContent={"center"}
            h={"full"}
            flex={1}
            alignItems={"center"}
            space={moderateScale(10)}>
            <MaterialCommunityIcons
              name="dialpad"
              size={moderateScale(30)}
              color="white"
            />
            <Text color={"white"}>USSD Codes</Text>
          </VStack>
        </HStack>
      </View>
    </View>
  );
};

export default Home;
