import { Box } from "@/components/ui/box";
import { HStack } from "@/components/ui/hstack";
import { ScrollView } from "@/components/ui/scroll-view";
import { View } from "@/components/ui/view";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { Divider } from "@/components/ui/divider";
import { Button, ButtonText } from "@/components/ui/button";
import React from "react";
import { SafeAreaView } from "react-native";
import { moderateScale } from "react-native-size-matters";
import {
  Octicons,
  Ionicons,
  FontAwesome5,
  MaterialCommunityIcons,
} from "@expo/vector-icons";

const Home = () => {
  return (
    <View className="h-full bg-amber-300 w-full">
      <Box
        className="bg-orange-700 w-full"
        style={{ height: moderateScale(100), padding: moderateScale(20) }}>
        <Button className="bg-orange-600">
          <ButtonText className="text-white">Enquire Balance</ButtonText>
        </Button>
      </Box>
      <View
        className="bg-orange-400 flex-1 w-full"
        style={{ padding: moderateScale(20) }}>
        <HStack className="justify-around flex-1">
          <VStack
            className="items-center justify-center flex-1 h-full"
            style={{ gap: moderateScale(10) }}>
            <FontAwesome5
              name="sim-card"
              size={moderateScale(30)}
              color="white"
            />
            <Text className="text-white">Reset Puk</Text>
          </VStack>
          <Divider
            className="bg-gray-300"
            orientation="vertical" />
        </HStack>

          <VStack
            className="h-full flex-1 justify-center items-center"
            style={{ gap: moderateScale(10) }}>
            <FontAwesome5 name="link" size={moderateScale(30)} color="white" />
            <Text className="text-white">Link Numbers</Text>
          </VStack>
        </HStack>

        <Divider className="bg-gray-300" />
        <HStack className="justify-around flex-1">
          <VStack
            className="justify-center items-center flex-1 h-full"
            style={{ gap: moderateScale(10) }}>
            <FontAwesome5 name="lock" size={moderateScale(30)} color="white" />
            <Text className="text-white">Change Password</Text>
          </VStack>
          <Divider
            className="bg-gray-300"
            orientation="vertical" />

          <VStack
            className="justify-center h-full flex-1 items-center"
            style={{ gap: moderateScale(10) }}>
            <MaterialCommunityIcons
              name="form-textbox-password"
              size={moderateScale(30)}
              color="white"
            />
            <Text className="text-white">Change Passphrase</Text>
          </VStack>
        </HStack>
        <Divider className="bg-gray-300" />
        <HStack className="justify-around flex-1">
          <VStack
            className="justify-center items-center flex-1 h-full"
            style={{ gap: moderateScale(10) }}>
            <FontAwesome5
              name="user-alt"
              size={moderateScale(30)}
              color="white"
            />
            <Text className="text-white">My Profile</Text>
          </VStack>
          <Divider
            className="bg-gray-300"
            orientation="vertical" />

          <VStack
            className="justify-center h-full flex-1 items-center"
            style={{ gap: moderateScale(10) }}>
            <MaterialCommunityIcons
              name="dialpad"
              size={moderateScale(30)}
              color="white"
            />
            <Text className="text-white">USSD Codes</Text>
          </VStack>
        </HStack>
      </View>
    </View>
  );
};

export default Home;
