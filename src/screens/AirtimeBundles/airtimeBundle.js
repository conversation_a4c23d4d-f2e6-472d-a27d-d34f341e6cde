import { <PERSON><PERSON><PERSON>, Feather } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
  ActionsheetFlatList,
  ActionsheetItem,
  ActionsheetItemText,
} from "../../../components/ui/actionsheet";
import { Badge } from "../../../components/ui/badge";
import { Box } from "../../../components/ui/box";
import { Button, ButtonText } from "../../../components/ui/button";
import { Card } from "../../../components/ui/card";
import { Center } from "../../../components/ui/center";
import {
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
} from "../../../components/ui/form-control";
import { HStack } from "../../../components/ui/hstack";
import { Input, InputField } from "../../../components/ui/input";
import { Pressable } from "../../../components/ui/pressable";
import { ScrollView } from "../../../components/ui/scroll-view";
import {
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
} from "../../../components/ui/select";
import { Text } from "../../../components/ui/text";
import { VStack } from "../../../components/ui/vstack";
import { View } from "../../../components/ui/view";
import React, { useEffect, useState } from "react";
import { SafeAreaView } from "react-native";
import * as Cont from "expo-contacts";
import Toast from "react-native-root-toast";
import { moderateScale } from "react-native-size-matters";
import LoadingModal from "../../components/Loading/LoadingModal";
import GetContacts from "../../components/Modals/contacts";
import airtimeBundles from "../../services/Bundles/Bundles";
import BundleFetchFailed from "../../components/Modals/BundleFetchFailed";
import store from "../../utilities/store";

const AirtimeBundle = ({ route }) => {
  const [amount, setAmount] = useState();
  const [number, setNumber] = useState("");
  const [errorNumber, setErrorNumber] = useState();
  const [loading, setLoading] = useState(false);
  const [failed, setFailed] = useState(false);

  const navigation = useNavigation();
  const [isYesSelected, setIsYesSelected] = useState(true);
  const [isNoSelected, setIsNoSelected] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [openContacts, setOpenContacts] = useState(false);
  const [contacts, setContacts] = useState([]);
  const [searchText, setSearchText] = useState("");
  const [loadingContacts] = useState(false);
  const [errorBundle, setErrorBundle] = useState(false);
  const [bundles, setBundles] = useState([]);
  const [BundleType, setBundleType] = useState("");
  const [BundleTypeError, setBundleTypeError] = useState("");
  const [selectedBundle, setSelectedBundle] = useState();
  const [bundleClasses, setBundleClasses] = useState([]);
  const [failedToFetch, setFailedToFetch] = useState(false);

  const [showContactsSheet, setShowContactsSheet] = useState(false);
  const [showBundlesSheet, setShowBundlesSheet] = useState(false);

  const validate = () => {
    const numberRegexp = /^(071|71)/;

    if (!BundleType) {
      setBundleTypeError("Please select a bundleType");
      return false;
    }

    //check mobile number
    if (!number && isNoSelected) {
      setErrorNumber("This field is required");
      return false;
    }

    if (!numberRegexp.test(number) && isNoSelected) {
      setErrorNumber("Invalid phone number, should be a netone number");
      return false;
    }

    if (number.length < 9 && isNoSelected) {
      setErrorNumber("Invalid phone number, not enough digits");
      return false;
    }

    if (number.length > 10 && isNoSelected) {
      setErrorNumber("Invalid phone number, too many digits");
      return false;
    }

    if (!selectedBundle) {
      setErrorBundle("Please select a bundle");
      return false;
    }

    return true;
  };

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [failed]);

  const handleYesPress = () => {
    setIsYesSelected(true);
    setIsNoSelected(false);
  };

  const handleNoPress = () => {
    setIsNoSelected(true);
    setIsYesSelected(false);
  };

  useEffect(() => {
    (async () => {
      const bundles = await store.getData("@bundles");

      if (bundles !== null) {
        setBundles(bundles);
        const bundleClasses = new Set();
        bundles.forEach((bundle) => {
          if (bundle.active) {
            bundleClasses.add(bundle.bundleClass);
          }
        });
        const uniqueBundleClasses = Array.from(bundleClasses);
        setBundleClasses(uniqueBundleClasses);
      } else {
        handleFetchBundles();
      }
    })();
  }, []);

  const handleLookUp = () => {
    const res = validate();
    if (!res) {
      return;
    }

    const allData = {
      buyerPhoneNumber: route.params.phone,
      receiverPhoneNumber: isYesSelected
        ? route.params.phone
        : "263" + parseInt(number),
      amount: amount,
      bundle: selectedBundle,
      other: isNoSelected,
    };

    navigation.navigate("confirmAirtimeBundle", {
      data: allData,
    });
  };

  const handleFetchBundles = async () => {
    try {
      setLoading(true);
      // useLayoutEffect
      const response = await airtimeBundles.GetBundles();
      if (response.data.success === true) {
        //set bundles
        setBundles(response.data.body);

        //save in state
        store.saveData("@bundles", response.data.body);
        // Create an empty set to store unique bundle classes
        const bundleClasses = new Set();
        // Loop through the bundles and add the bundleClass to the set if active is true
        response.data.body.forEach((bundle) => {
          if (bundle.active) {
            bundleClasses.add(bundle.bundleClass);
          }
        });

        // Convert the set to an array if needed
        const uniqueBundleClasses = Array.from(bundleClasses);
        setBundleClasses(uniqueBundleClasses);
      } else {
        setFailed(true);
        setFailedToFetch(true);
        setErrorMessage("Failed to fetch bundles");
      }

      setLoading(false);
    } catch (error) {
      console.log(error);

      setFailed(true);
      setFailedToFetch(true);

      setLoading(false);
      if (error.response.data) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  const filterData = (data) => {
    return data.filter((item) =>
      item?.givenName?.toLowerCase().includes(searchText.toLowerCase())
    );
  };

  useEffect(() => {
    (async () => {
      const { status } = await Cont.requestPermissionsAsync();
      if (status === "granted") {
        const { data } = await Cont.getContactsAsync();

        if (data.length > 0) {
          setContacts(data);
        }
      }
    })();
  }, []);

  const filterBundles = () => {
    return bundles.filter((item) => {
      if (item.active === true) {
        return item.bundleClass
          .toLowerCase()
          .includes(BundleType.toLowerCase());
      }
    });
  };

  return (
    <SafeAreaView>
      <ScrollView showsVerticalScrollIndicator={false}>
        {failedToFetch && loading == false && (
          <View p={moderateScale(50)}>
            <BundleFetchFailed fetchBundles={handleFetchBundles} />
          </View>
        )}
        <View className="w-full bg-white min-h-full">
          <Box className="px-5 py-3 w-full">
            <LoadingModal isLoading={loading} />

            <Toast
              visible={failed}
              position={70}
              shadow={true}
              animation={true}
              hideOnPress={true}
              backgroundColor={"red"}
              opacity={0.9}
              duration={Toast.durations.LONG}>
              {errorMessage}
            </Toast>

            <VStack className="space-y-4 mt-12">
              <VStack className="space-y-6">
                <FormControl className="mb-4">
                  <FormControlLabel>
                    <FormControlLabelText className="text-base font-semibold text-gray-800 mb-2">
                      Buy For:
                    </FormControlLabelText>
                  </FormControlLabel>
                  <HStack space={3} className="w-full">
                    <Pressable
                      onPress={handleYesPress}
                      className="flex-1"
                      style={{ opacity: isYesSelected ? 1 : 0.8 }}>
                      <Card
                        variant={isYesSelected ? "elevated" : "outline"}
                        className={`h-12 justify-center items-center ${
                          isYesSelected
                            ? "bg-orange-500 border-orange-500"
                            : "bg-gray-50 border-gray-200"
                        }`}>
                        <Text
                          className={`font-medium text-base ${
                            isYesSelected ? "text-white" : "text-gray-700"
                          }`}
                          fontFamily={"openSansMedium"}>
                          Self
                        </Text>
                      </Card>
                    </Pressable>
                    <Pressable
                      onPress={handleNoPress}
                      className="flex-1"
                      style={{ opacity: isNoSelected ? 1 : 0.8 }}>
                      <Card
                        variant={isNoSelected ? "elevated" : "outline"}
                        className={`h-12 justify-center items-center ${
                          isNoSelected
                            ? "bg-orange-500 border-orange-500"
                            : "bg-gray-50 border-gray-200"
                        }`}>
                        <Text
                          className={`font-medium text-base ${
                            isNoSelected ? "text-white" : "text-gray-700"
                          }`}
                          fontFamily={"openSansMedium"}>
                          Other
                        </Text>
                      </Card>
                    </Pressable>
                  </HStack>
                </FormControl>

                {isNoSelected && (
                  <FormControl>
                    <FormControlLabel>
                      <FormControlLabelText className="text-base font-medium text-gray-700 mb-2">
                        Receiver's Mobile Number
                      </FormControlLabelText>
                    </FormControlLabel>
                    <Input
                      className={`${
                        errorNumber ? "border-red-400" : "border-gray-300"
                      }`}>
                      <Text className="absolute left-3 z-10 text-base font-medium text-gray-600">
                        +263
                      </Text>
                      <InputField
                        className="pl-16 text-base"
                        defaultValue={`${number}`}
                        onFocus={() => {
                          setErrorNumber("");
                        }}
                        placeholder="Receiver's Netone Mobile Number"
                        onChangeText={(text) => setNumber(text)}
                        keyboardType={"number-pad"}
                      />
                    </Input>
                    <Pressable
                      onPress={() => {
                        setShowContactsSheet(true);
                      }}
                      className="mt-3">
                      <Text className="text-blue-500 text-base font-medium">
                        📱 Select From Contacts
                      </Text>
                    </Pressable>
                    <FormControlError>
                      <FormControlErrorText className="text-red-500 text-sm mt-1">
                        {errorNumber}
                      </FormControlErrorText>
                    </FormControlError>
                  </FormControl>
                )}

                <FormControl>
                  <FormControlLabel>
                    <FormControlLabelText className="text-base font-medium text-gray-700 mb-2">
                      Bundle Type
                    </FormControlLabelText>
                  </FormControlLabel>
                  <Select
                    selectedValue={BundleType}
                    onValueChange={(itemValue) => setBundleType(itemValue)}>
                    <SelectTrigger
                      variant="outline"
                      size="md"
                      className={`${
                        BundleTypeError ? "border-red-400" : "border-gray-300"
                      } h-12`}>
                      <SelectInput
                        placeholder="Select Bundle Type"
                        className="text-base"
                      />
                      <SelectIcon className="mr-3" />
                    </SelectTrigger>
                    <SelectPortal>
                      <SelectBackdrop />
                      <SelectContent>
                        <SelectDragIndicatorWrapper>
                          <SelectDragIndicator />
                        </SelectDragIndicatorWrapper>
                        {bundleClasses.map((item) => (
                          <SelectItem key={item} label={item} value={item} />
                        ))}
                      </SelectContent>
                    </SelectPortal>
                  </Select>
                  <FormControlError>
                    <FormControlErrorText className="text-red-500 text-sm mt-1">
                      {BundleTypeError}
                    </FormControlErrorText>
                  </FormControlError>
                </FormControl>

                {BundleType && (
                  <FormControl>
                    <FormControlLabel>
                      <FormControlLabelText className="text-base font-medium text-gray-700 mb-2">
                        Bundle Plan
                      </FormControlLabelText>
                    </FormControlLabel>
                    <Pressable
                      onPress={() => {
                        setShowBundlesSheet(true);
                      }}>
                      <Card
                        variant="outline"
                        className={`h-12 px-4 justify-center ${
                          errorBundle ? "border-red-400" : "border-gray-300"
                        } hover:border-orange-300 transition-colors`}>
                        <HStack className="justify-between items-center">
                          <Text
                            className={`text-base ${
                              selectedBundle ? "text-gray-700" : "text-gray-400"
                            }`}
                            fontFamily={"openSansMedium"}>
                            {selectedBundle
                              ? selectedBundle.name
                              : "Select Bundle Plan"}
                          </Text>
                          {!selectedBundle && (
                            <Octicons
                              name="single-select"
                              size={moderateScale(20)}
                              color="gray"
                            />
                          )}
                        </HStack>
                      </Card>
                    </Pressable>
                    {errorBundle && (
                      <FormControlError>
                        <FormControlErrorText className="text-red-500 text-sm mt-1">
                          {errorBundle}
                        </FormControlErrorText>
                      </FormControlError>
                    )}
                  </FormControl>
                )}
                {selectedBundle && (
                  <Card className="p-4 bg-orange-50 border border-orange-200">
                    <VStack space={2}>
                      <Text className="text-orange-800 font-semibold text-base">
                        Selected Bundle
                      </Text>
                      <Text className="text-gray-700 font-medium">
                        {selectedBundle.displayText}
                      </Text>
                      <Text className="text-orange-600 font-bold text-lg">
                        USD {selectedBundle.usdAmount}
                      </Text>
                    </VStack>
                  </Card>
                )}
              </VStack>

              <Button
                onPress={() => handleLookUp()}
                className="mt-6 h-12 bg-orange-500 rounded-lg shadow-lg active:bg-orange-600">
                <ButtonText className="text-white font-semibold text-base">
                  Continue
                </ButtonText>
              </Button>

              {openContacts && (
                <GetContacts
                  open={openContacts}
                  setNumber={(value) => {
                    setNumber(value);
                  }}
                  close={() => {
                    setOpenContacts(false);
                  }}></GetContacts>
              )}

              <Actionsheet
                isOpen={showContactsSheet}
                onClose={() => setShowContactsSheet(false)}>
                <ActionsheetBackdrop />
                <ActionsheetContent className="max-h-[85%]">
                  <ActionsheetDragIndicatorWrapper>
                    <ActionsheetDragIndicator />
                  </ActionsheetDragIndicatorWrapper>
                  <HStack className="w-full justify-between items-center p-4">
                    <Text className="text-lg font-semibold">
                      Select Contact
                    </Text>
                    <Pressable
                      onPress={() => setShowContactsSheet(false)}
                      className="p-1">
                      <Feather name="x" size={moderateScale(24)} color="gray" />
                    </Pressable>
                  </HStack>
                  <View className="w-full px-4 pb-4">
                    <Input className="my-4">
                      <InputField
                        value={searchText}
                        placeholder="Search Contact"
                        onChangeText={(text) => setSearchText(text)}
                        className="text-base"
                      />
                    </Input>
                    {loadingContacts && (
                      <Center className="py-8">
                        <Text className="text-blue-500 text-base">
                          Getting Contacts ...
                        </Text>
                      </Center>
                    )}
                    {filterData(contacts).length == 0 && !loadingContacts && (
                      <Center className="py-8">
                        <Text className="text-red-500 text-base font-medium">
                          No Contacts Found
                        </Text>
                      </Center>
                    )}
                    {filterData(contacts).length > 0 && !loadingContacts && (
                      <Text className="text-gray-500 text-base font-medium mb-4">
                        Tap on a contact to select
                      </Text>
                    )}
                    {filterData(contacts).length > 0 && !loadingContacts && (
                      <ActionsheetFlatList
                        data={filterData(contacts)}
                        estimatedItemSize={70}
                        showsVerticalScrollIndicator={false}
                        className="max-h-96"
                        renderItem={({ item }) => {
                          if (
                            item.contactType === "person" &&
                            item.phoneNumbers
                          ) {
                            return (
                              <ActionsheetItem
                                onPress={() => {
                                  setNumber(
                                    item.phoneNumbers?.[0].number
                                      .toString()
                                      .replace(/\s/g, "")
                                      .replace("+263", "")
                                  );
                                  setShowContactsSheet(false);
                                }}
                                className="mb-2">
                                <Card className="w-full p-4">
                                  <ActionsheetItemText className="text-base">
                                    {`${item?.name}`} -{" "}
                                    {`${item?.phoneNumbers[0]?.number}`}
                                  </ActionsheetItemText>
                                </Card>
                              </ActionsheetItem>
                            );
                          }
                        }}
                      />
                    )}
                  </View>
                </ActionsheetContent>
              </Actionsheet>
              <Actionsheet
                isOpen={showBundlesSheet}
                onClose={() => setShowBundlesSheet(false)}>
                <ActionsheetBackdrop />
                <ActionsheetContent className="max-h-[85%]">
                  <ActionsheetDragIndicatorWrapper>
                    <ActionsheetDragIndicator />
                  </ActionsheetDragIndicatorWrapper>
                  <HStack className="w-full justify-between items-center p-4">
                    <Text className="text-lg font-semibold">Select Bundle</Text>
                    <Pressable
                      onPress={() => setShowBundlesSheet(false)}
                      className="p-1">
                      <Feather name="x" size={moderateScale(24)} color="gray" />
                    </Pressable>
                  </HStack>
                  <View className="w-full px-4 pb-4">
                    <ActionsheetFlatList
                      data={filterBundles()}
                      estimatedItemSize={120}
                      showsVerticalScrollIndicator={false}
                      className="max-h-96"
                      renderItem={({ item }) => (
                        <ActionsheetItem
                          onPress={() => {
                            setSelectedBundle(item);
                            setErrorBundle(false);
                            setAmount(item.usdAmount);
                            setShowBundlesSheet(false);
                          }}
                          className="mb-3">
                          <Card className="w-full p-4 bg-white border border-gray-200">
                            <Badge className="bg-orange-500 mb-2 self-start">
                              <Text className="text-white text-xs font-medium">
                                {item.bundleClass} Bundle
                              </Text>
                            </Badge>
                            <VStack space={1}>
                              <Text className="text-gray-700 font-medium text-base">
                                {item.displayText}
                              </Text>
                              <Text className="text-gray-700 font-medium text-base">
                                Amount: USD {item.usdAmount}
                              </Text>
                            </VStack>
                          </Card>
                        </ActionsheetItem>
                      )}
                      ListEmptyComponent={() => (
                        <Center className="py-8">
                          <Text className="text-gray-500 text-lg">
                            No Bundles Found
                          </Text>
                        </Center>
                      )}
                    />
                  </View>
                </ActionsheetContent>
              </Actionsheet>
            </VStack>
          </Box>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default AirtimeBundle;
