import { Center } from "@/components/ui/center";
import { Divider } from "@/components/ui/divider";
import { HStack } from "@/components/ui/hstack";
import { Heading } from "@/components/ui/heading";
import { ScrollView } from "@/components/ui/scroll-view";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { View } from "@/components/ui/view";
import React from "react";
import { moderateScale } from "react-native-size-matters";

// import { Container } from './styles';

const FAQ = () => {
  const data = [
    {
      title: "How do I recharge my NetOne line?",
      data: [
        "(a)Dial *133*Enter PIN# ",
        "(b) Dial *171#, select option (6) Airtime Recharge > Select option (1) Self > Enter Voucher PIN then Confirm",
        "(c) You may purchase airtime via OneMoney *111#",
      ],
    },
    {
      title:
        "I over-scratched my recharge card, how do I retrieve the voucher PIN?",
      data: [
        "You can retrieve voucher PIN via our Self-Help portal by dialing *171# - Select Option 6 > Go to Option (3) - Damaged > Enter the serial number of the scratch card, then PIN will be displayed on-screen.",
      ],
    },
    {
      title: "My account is locked; how do I unlock it?",
      data: [
        "This means you have tried to recharge your account with a wrong or used voucher PIN. Kindly call our Contact Centre on 123 or 121 to get your account unlocked.",
      ],
    },
    {
      title: "How do I recharge another prepaid account?",
      data: [
        "Simply dial *133*Recharge PIN*Recipients number# or *171#, select option (6) Airtime Recharge > Select option (2) - Other > Enter Voucher PIN then Confirm.",
      ],
    },
    {
      title: "How do I check my airtime balance?",
      data: [
        "To check your airtime balance dial *171# - Select option (7) - Balance Enquiry > follow on screen instructions.",
      ],
    },
    {
      title: "How do I buy NetOne bundles?",
      data: [
        "Dial *171# and select the desired category and value option. Note all social media and SMS bundles are found under option (3) - Bundles",
      ],
    },
    {
      title: "How do I buy NetOne bundles?",
      data: [
        "Dial *171# and select the desired category and value option. Note all social media and SMS bundles are found under option (3) - Bundles.",
      ],
    },
  ];
  return (
    <View
      background={"white"}
      px={moderateScale(20)}
      py={moderateScale(2)}
      pt={moderateScale(10)}
      flex={1}>
      <SectionList
        maxW="300"
        w="100%"
        sections={data}
        keyExtractor={(item, index) => item + index}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: moderateScale(100) }}
        renderItem={({ item }) => (
          <Text py="2" bg={item} key={item} fontFamily={"openSansMedium"}>
            {item}
          </Text>
        )}
        renderSectionHeader={({ section: { title } }) => (
          <Text
            fontSize="lg"
            mt="8"
            pb="4"
            fontWeight={100}
            fontFamily={"openSansExtraBold"}>
            {title}
          </Text>
        )}
      />
    </View>
  );
};

export default FAQ;
