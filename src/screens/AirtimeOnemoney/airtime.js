import { <PERSON><PERSON><PERSON>, <PERSON>ather } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { Box } from "@/components/ui/box";
import { But<PERSON> } from "@/components/ui/button";
import { Center } from "@/components/ui/center";
import { FlatList } from "@/components/ui/flat-list";
import { FormControl } from "@/components/ui/form-control";
import { HStack } from "@/components/ui/hstack";
import { Input } from "@/components/ui/input";
import { Modal } from "@/components/ui/modal";
import { ScrollView } from "@/components/ui/scroll-view";
import { Select } from "@/components/ui/select";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { View } from "@/components/ui/view";
import React, { useEffect, useRef, useState } from "react";
import {
  Dimensions,
  PermissionsAndroid,
  Platform,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import ActionSheet from "react-native-actions-sheet";
import * as Cont from "expo-contacts";
import Toast from "react-native-root-toast";
import * as Animatable from "react-native-animatable";
import { moderateScale, scale, verticalScale } from "react-native-size-matters";
import CurrencyInputField from "../../components/CurrencyInput";
import LoadingModal from "../../components/Loading/LoadingModal";
import { FlashList } from "@shopify/flash-list";
import Auth from "../../services/Auth/Auth";

const Airtime = ({ route }) => {
  const [registered, setRegistered] = useState();
  const [prompt, setPropmt] = useState();
  const [amount, setAmount] = useState();
  const [errorAmount, setErrorAmount] = useState();
  const [currency, setCurrency] = useState();
  const [errorCurrency, setErrorCurrency] = useState();
  const [number, setNumber] = useState("");
  const [errorNumber, setErrorNumber] = useState();
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [failed, setFailed] = useState(false);
  const height = Dimensions.get("window").height;
  const navigation = useNavigation();
  const [isYesSelected, setIsYesSelected] = useState(true);
  const [isNoSelected, setIsNoSelected] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [openContacts, setOpenContacts] = useState(false);
  const [contacts, setContacts] = useState([]);
  const [contactsError, setContactsError] = useState("");
  const [searchText, setSearchText] = useState("");
  const [loadingContacts, setLoadingContacts] = useState(false);
  const [selectedAmount, setSelectedAmount] = useState();

  const actionSheetRef = useRef(null);

  const validate = () => {
    const numberRegexp = /^(071|71)/;

    //check mobile number
    if (!number && isNoSelected) {
      setErrorNumber("This field is required");
      return false;
    }

    if (!numberRegexp.test(number) && isNoSelected) {
      setErrorNumber("Invalid phone number, should be a netone number");
      return false;
    }

    if (number.length < 9 && isNoSelected) {
      setErrorNumber("Invalid phone number, not enough digits");
      return false;
    }

    if (number.length > 10 && isNoSelected) {
      setErrorNumber("Invalid phone number, too many digits");
      return false;
    }

    //check currency
    if (!currency) {
      setErrorCurrency("This field is required");
      return false;
    }

    //check amount
    if (!amount) {
      setErrorAmount("This field is required");
      return false;
    }

    if (amount < 1 && currency === "ZiG") {
      setErrorAmount("Amount must be greater than 1");
      return false;
    }

    return true;
  };

  useEffect(() => {
    setErrorAmount("");
  }, [amount]);

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [failed]);

  const handleYesPress = () => {
    setIsYesSelected(true);
    setIsNoSelected(false);
  };

  const handleNoPress = () => {
    setIsNoSelected(true);
    setIsYesSelected(false);
  };

  const handleLookUp = async () => {
    try {
      const res = validate();
      if (!res) {
        return;
      }
      setLoading(true);
      setFailed(false);
      const data = isYesSelected
        ? route.params.phone.toString().replace(/\s/g, "").replace("263", "")
        : parseInt(number);
      const allData = {
        buyerPhoneNumber: route.params.phone,
        receiverPhoneNumber: isYesSelected
          ? route.params.phone
          : "263" + parseInt(number),
        currency: currency,
        amount: amount,
        other: isNoSelected,
      };

      const response = await Auth.customerLookup(data);

      if (response.status === 200) {
        if (response.data) {
          navigation.navigate("ConfirmAirtime", {
            data: allData,
            userDetails: response.data,
          });
        } else {
          setFailed(true);
          setErrorMessage(
            "You are not registered with OneMoney, please register to use this service."
          );
        }
      } else {
        setFailed(true);
        setErrorMessage("Something went wrong, Please try again");
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setFailed(true);
      console.log(error);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  const filterData = (data) => {
    return data.filter((item) =>
      item?.name?.toLowerCase().includes(searchText.toLowerCase())
    );
  };

  useEffect(() => {
    (async () => {
      const { status } = await Cont.requestPermissionsAsync();
      if (status === "granted") {
        const { data } = await Cont.getContactsAsync();

        if (data.length > 0) {
          setContacts(data);
        }
      }
    })();
  }, []);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <LoadingModal isLoading={loading} />
      <View showsVerticalScrollIndicator={false} flex={1}>
        <View w="100%" bg={"white"} h={height}>
          <Box px={moderateScale(20)} py={moderateScale(10)} w="100%">
            <Toast
              visible={failed}
              position={70}
              shadow={true}
              animation={true}
              hideOnPress={true}
              backgroundColor={"red"}
              opacity={0.9}
              duration={Toast.durations.LONG}>
              {errorMessage}
            </Toast>

            <VStack space={moderateScale(16)} mt={moderateScale(48)}>
              <VStack space={4}>
                <FormControl mb={moderateScale(16)}>
                  <FormControl.Label>
                    <Text
                      fontSize={moderateScale(15)}
                      my={moderateScale(4)}
                      fontFamily={"openSansSemiBold"}>
                      Buy For:{" "}
                    </Text>
                  </FormControl.Label>
                  <HStack space={4}>
                    <TouchableOpacity
                      onPress={handleYesPress}
                      style={{ width: "40%", flex: 1 }}>
                      <View
                        h={verticalScale(48)}
                        rounded="lg"
                        borderColor="gray.300"
                        style={{
                          width: "100%",
                          borderWidth: 0,
                          alignItems: "center",
                          justifyContent: "center",
                        }}>
                        {isYesSelected ? (
                          <View
                            rounded="lg"
                            style={{
                              width: "100%",
                              height: "100%",
                              backgroundColor: "#F68C1E",
                              alignItems: "center",
                              justifyContent: "center",
                            }}>
                            <Text
                              color={"white"}
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(15)}>
                              Self
                            </Text>
                          </View>
                        ) : (
                          <View
                            rounded="lg"
                            bgColor={"gray.100"}
                            style={{
                              width: "100%",
                              height: "100%",
                              alignItems: "center",
                              justifyContent: "center",
                            }}>
                            <Text
                              color={"black"}
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(15)}>
                              Self
                            </Text>
                          </View>
                        )}
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={handleNoPress}
                      style={{ width: "40%", flex: 1 }}>
                      <View
                        h={verticalScale(48)}
                        rounded="lg"
                        borderColor="gray.300"
                        style={{
                          width: "100%",
                          borderWidth: 0,
                          alignItems: "center",
                          justifyContent: "center",
                        }}>
                        {isNoSelected ? (
                          <View
                            rounded="lg"
                            style={{
                              width: "100%",
                              height: "100%",
                              backgroundColor: "#F68C1E",
                              alignItems: "center",
                              justifyContent: "center",
                            }}>
                            <Text
                              color={"white"}
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(15)}>
                              Other
                            </Text>
                          </View>
                        ) : (
                          <View
                            rounded="lg"
                            bgColor={"gray.100"}
                            style={{
                              width: "100%",
                              height: "100%",
                              alignItems: "center",
                              justifyContent: "center",
                            }}>
                            <Text
                              color={"black"}
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(15)}>
                              Other
                            </Text>
                          </View>
                        )}
                      </View>
                    </TouchableOpacity>
                  </HStack>
                </FormControl>

                {isNoSelected && (
                  <FormControl>
                    <InputGroup>
                      <InputLeftAddon
                        roundedLeft={"lg"}
                        w={scale(60)}
                        children={
                          <Text
                            fontSize={moderateScale(15)}
                            fontFamily={"openSansMedium"}
                            color={"gray.600"}>
                            {"+263"}
                          </Text>
                        }
                      />
                      <Input
                        flex={1}
                        defaultValue={`${number}`}
                        fontFamily={"openSansSemiBold"}
                        onFocus={() => {
                          setErrorNumber("");
                        }}
                        focusOutlineColor={"#F68C1E"}
                        borderColor={errorNumber ? "red.400" : "gray.300"}
                        placeholder="Receivers Netone Number"
                        fontSize={moderateScale(15)}
                        bgColor={"gray.100"}
                        rounded={"lg"}
                        onChangeText={(text) => setNumber(text)}
                        h={verticalScale(48)}
                        keyboardType={"number-pad"}
                      />
                    </InputGroup>
                    <TouchableOpacity
                      onPress={() => {
                        actionSheetRef.current?.show();
                      }}>
                      <Text color={"blue.400"} fontSize={moderateScale(15)}>
                        Select From Contacts
                      </Text>
                    </TouchableOpacity>
                    <Text color={"red.400"} fontSize={moderateScale(15)}>
                      {errorNumber}
                    </Text>
                  </FormControl>
                )}

                <VStack w="100%" space={4}>
                  <View>
                    <Select
                      selectedValue={currency}
                      onOpen={() => {
                        setErrorCurrency("");
                      }}
                      width={"100%"}
                      bgColor={"gray.100"}
                      rounded={"lg"}
                      fontSize={moderateScale(15)}
                      fontFamily={"openSansMedium"}
                      h={verticalScale(48)}
                      onFocus={() => {
                        setErrorCurrency("");
                      }}
                      focusOutlineColor={"#F68C1E"}
                      borderColor={errorCurrency ? "red.400" : "gray.300"}
                      accessibilityLabel="Currency"
                      placeholder="Currency"
                      _selectedItem={{
                        bg: "teal.600",
                      }}
                      onValueChange={(itemValue) => setCurrency(itemValue)}>
                      <Select.Item label="ZiG" value="ZiG" />
                      <Select.Item label="USD" value="USD" />
                    </Select>
                    <Text color={"red.400"} fontSize={moderateScale(15)}>
                      {errorCurrency}
                    </Text>
                  </View>

                  {currency === "ZiG" && (
                    <CurrencyInputField
                      decimalPlaces={2}
                      onChange={(value) => {
                        setAmount(value);
                        setErrorAmount("");
                      }}
                      error={errorAmount}
                    />
                  )}
                  {currency === "USD" && (
                    <View>
                      <Select
                        selectedValue={selectedAmount}
                        width={"100%"}
                        bgColor={"gray.100"}
                        rounded={"lg"}
                        fontSize={moderateScale(15)}
                        fontFamily={"openSansMedium"}
                        h={verticalScale(48)}
                        focusOutlineColor={"#F68C1E"}
                        borderColor={errorCurrency ? "red.400" : "gray.300"}
                        accessibilityLabel="Amount"
                        placeholder="Select Amount"
                        _selectedItem={{
                          bg: "teal.600",
                        }}
                        onValueChange={(itemValue) => {
                          setAmount(itemValue.toString());
                          setSelectedAmount(itemValue);
                        }}>
                        <Select.Item label="$0.50" value="05" />
                        <Select.Item label="$1.00" value="1" />
                        <Select.Item label="$2.00" value="2" />
                        <Select.Item label="$5.00" value="5" />
                        <Select.Item label="$10.00" value="10" />
                        <Select.Item label="$20.00" value="20" />
                        <Select.Item label="$50.00" value="50" />
                      </Select>
                      <Text color={"red.400"} fontSize={moderateScale(15)}>
                        {errorCurrency}
                      </Text>
                    </View>
                  )}
                </VStack>
              </VStack>

              <Button
                onPressIn={() => handleLookUp()}
                mt={moderateScale(24)}
                onPress={() => handleLookUp()}
                h={verticalScale(48)}
                bg="#F68C1E"
                rounded={"lg"}>
                <Text
                  fontSize={moderateScale(15)}
                  color="white"
                  fontFamily={"openSansSemiBold"}>
                  Buy Airtime
                </Text>
              </Button>

              <Modal isOpen={showModal} onClose={() => setShowModal(false)}>
                <Modal.Content maxWidth="400px">
                  <Modal.CloseButton />
                  <Modal.Body>
                    <FormControl>
                      <FormControl.Label>Favorites Name</FormControl.Label>
                      <Input />
                    </FormControl>
                  </Modal.Body>
                  <Modal.Footer>
                    <Button.Group space={2}>
                      <Button
                        variant="ghost"
                        colorScheme="blueGray"
                        onPress={() => {
                          setShowModal(false);
                        }}>
                        Cancel
                      </Button>
                      <Button
                        onPress={() => {
                          setShowModal(false);
                        }}>
                        Save
                      </Button>
                    </Button.Group>
                  </Modal.Footer>
                </Modal.Content>
              </Modal>

              <ActionSheet
                ref={actionSheetRef}
                containerStyle={{
                  borderTopLeftRadius: 25,
                  borderTopRightRadius: 25,
                }}
                indicatorStyle={{
                  width: 100,
                }}
                gestureEnabled={false}>
                <HStack p={moderateScale(10)}>
                  <TouchableOpacity
                    onPress={() => {
                      actionSheetRef.current?.hide();
                    }}>
                    <Feather name="x" size={moderateScale(24)} color="gray" />
                  </TouchableOpacity>
                </HStack>
                <View
                  w="100%"
                  h={"88%"}
                  px={moderateScale(16)}
                  pt={moderateScale(4)}>
                  <Input
                    fontFamily={"openSansSemiBold"}
                    my={moderateScale(20)}
                    value={searchText}
                    focusOutlineColor={"#F68C1E"}
                    borderColor={"gray.300"}
                    placeholder="Search Contact"
                    fontSize={moderateScale(15)}
                    bgColor={"gray.100"}
                    rounded={"lg"}
                    onChangeText={(text) => setSearchText(text)}
                    h={verticalScale(48)}
                  />
                  {loadingContacts && (
                    <Center>
                      <Text color={"blue.500"}>Getting Contacts ...</Text>
                    </Center>
                  )}
                  {filterData(contacts).length == 0 && !loadingContacts && (
                    <Center>
                      <Text
                        color={"red.500"}
                        fontFamily={"openSansMedium"}
                        fontWeight="500"
                        fontSize={moderateScale(16)}
                        my={moderateScale(15)}>
                        No Contacts Found
                      </Text>
                    </Center>
                  )}
                  {filterData(contacts).length > 0 && !loadingContacts && (
                    <Text
                      color={"gray.500"}
                      fontFamily={"openSansMedium"}
                      fontWeight="500"
                      fontSize={moderateScale(16)}
                      my={moderateScale(15)}>
                      Tap on a contact to select
                    </Text>
                  )}
                  {filterData(contacts).length > 0 && !loadingContacts && (
                    <Animatable.View
                      easing="ease-out"
                      animation={"fadeInUp"}
                      delay={300}>
                      <View height={"100%"} width={"100%"}>
                        <FlashList
                          data={filterData(contacts)}
                          estimatedItemSize={200}
                          showsVerticalScrollIndicator={false}
                          contentContainerStyle={{
                            paddingBottom: moderateScale(200),
                          }}
                          renderItem={({ item }) => {
                            if (
                              item.contactType === "person" &&
                              item.phoneNumbers
                            ) {
                              return (
                                <View key={item} h={verticalScale(70)}>
                                  <View
                                    style={styles.MainContainer}
                                    bgColor={"white"}
                                    mb={moderateScale(15)}
                                    rounded={"xl"}
                                    shadow={0.5}
                                    borderColor={"gray.200"}
                                    borderWidth={1}>
                                    <TouchableOpacity
                                      onPress={() => {
                                        setNumber(
                                          item.phoneNumbers?.[0].number
                                            .toString()
                                            .replace(/\s/g, "")
                                            .replace("+263", "")
                                        );
                                        actionSheetRef.current?.hide();
                                      }}>
                                      <Center h={"100%"}>
                                        <Text style={{ fontSize: 18 }}>
                                          {" "}
                                          {`${item && item?.name}`} -{" "}
                                          {`${
                                            item &&
                                            item?.phoneNumbers[0]?.number
                                          }`}
                                        </Text>
                                      </Center>
                                    </TouchableOpacity>
                                  </View>
                                </View>
                              );
                            }
                          }}
                          numColumns={1}
                        />
                      </View>
                    </Animatable.View>
                  )}
                </View>
              </ActionSheet>
            </VStack>
          </Box>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  MainContainer: {
    flex: 1,
    paddingTop: Platform.OS === "ios" ? 20 : 0,
  },

  title: {
    padding: 12,
    fontSize: 22,
    backgroundColor: "#33691E",
    color: "white",
  },

  contactTitle: {
    fontSize: 22,
    paddingTop: 8,
    paddingBottom: 8,
    paddingLeft: 15,
    color: "black",
  },

  row: {
    flexDirection: "row",
    height: 60,
  },

  avatarContainer: {
    marginLeft: 12,
    justifyContent: "center",
    alignItems: "center",
  },

  listTextContainer: {
    marginLeft: 15,
    flexDirection: "row",
    flex: 1,
  },
});

export default Airtime;
