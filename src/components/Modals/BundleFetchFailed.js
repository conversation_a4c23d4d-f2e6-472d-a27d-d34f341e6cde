import { useNavigation } from "@react-navigation/native";
import LottieView from "lottie-react-native";
import { Button, ButtonText } from "@/components/ui/button";
import { HStack } from "@/components/ui/hstack";
import {
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalBody,
} from "@/components/ui/modal";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import React, { useRef, useState } from "react";
import { moderateScale, verticalScale } from "react-native-size-matters";
import { Entypo, Octicons, Feather } from "@expo/vector-icons";

const BundleFetchFailed = ({ fetchBundles }) => {
  const [placement, setPlacement] = useState(undefined);
  const animation = useRef(null);
  const [open, setOpen] = useState(true);
  const navigation = useNavigation();

  const openModal = (placement) => {
    setOpen(true);
    setPlacement(placement);
  };

  return (
    <>
      <Modal isOpen={open} size="xl">
        <ModalBackdrop />
        <ModalContent className="max-w-96 rounded-xl bg-white">
          <ModalBody>
            <VStack
              className="items-center px-2 py-2"
              style={{ gap: moderateScale(10) }}>
              <Text
                style={{
                  fontSize: moderateScale(17),
                  color: "#6B7280",
                  fontFamily: "openSansBold",
                }}>
                Failed To Fetch Bundles
              </Text>
              <Button
                className="w-full bg-gray-300 rounded-lg"
                style={{
                  marginTop: moderateScale(1),
                  height: verticalScale(48),
                }}
                onPress={() => {
                  fetchBundles();
                }}>
                <HStack
                  className="items-center"
                  style={{ gap: moderateScale(10) }}>
                  <ButtonText
                    style={{
                      fontSize: moderateScale(15),
                      color: "black",
                      fontFamily: "openSansSemiBold",
                    }}>
                    Retry
                  </ButtonText>
                  <Feather name="refresh-cw" size={moderateScale(16)} />
                </HStack>
              </Button>
              <Button
                className="w-full bg-orange-500 rounded-lg"
                style={{
                  marginTop: moderateScale(1),
                  height: verticalScale(48),
                }}
                onPress={() => {
                  navigation.goBack();
                }}>
                <ButtonText
                  style={{
                    fontSize: moderateScale(15),
                    color: "white",
                    fontFamily: "openSansSemiBold",
                  }}>
                  Go to Home Page
                </ButtonText>
              </Button>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

export default BundleFetchFailed;
