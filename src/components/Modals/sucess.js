import LottieView from "lottie-react-native";
import { Button, ButtonText } from "@/components/ui/button";
import {
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalBody,
} from "@/components/ui/modal";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import React, { useRef, useState } from "react";
import { moderateScale, verticalScale } from "react-native-size-matters";

const Example = () => {
  const [placement, setPlacement] = useState(undefined);
  const animation = useRef(null);
  const [open, setOpen] = useState(false);

  const openModal = (placement) => {
    setOpen(true);
    setPlacement(placement);
  };

  return (
    <>
      <Stack
        direction={{
          base: "column",
          md: "row",
        }}
        space={2}>
        <Button onPress={() => openModal("center")}>Center</Button>
      </Stack>

      <Modal useRNModal={true} isOpen={open} safeAreaTop={true} size={"full"}>
        <Modal.Content maxWidth="350" rounded={"xl"} bg={"white"}>
          <Modal.Body>
            <VStack alignContent={"center"} alignItems={"center"} px={5} py={2}>
              <Text
                fontSize={moderateScale(20)}
                color={"gray.600"}
                fontFamily={"openSansBold"}>
                Money sent successfully!
              </Text>
              <LottieView
                autoPlay
                ref={animation}
                style={{
                  width: 200,
                  height: 200,
                  backgroundColor: "white",
                }}
                // Find more Lottie files at https://lottiefiles.com/featured
                source={require("../../assets/lottie/success.json")}
              />
              <Button
                mt={moderateScale(1)}
                w={"100%"}
                onPress={() => {
                  setOpen(false);
                }}
                h={verticalScale(48)}
                bg="#F68C1E"
                rounded={"lg"}>
                <Text
                  fontSize={moderateScale(15)}
                  color="white"
                  fontFamily={"openSansSemiBold"}>
                  Continue
                </Text>
              </Button>
            </VStack>
          </Modal.Body>
        </Modal.Content>
      </Modal>
    </>
  );
};

export default Example;
