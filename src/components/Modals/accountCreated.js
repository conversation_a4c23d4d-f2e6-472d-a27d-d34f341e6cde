import { useNavigation } from "@react-navigation/native";
import LottieView from "lottie-react-native";
import { Button, ButtonText } from "@/components/ui/button";
import {
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalBody,
} from "@/components/ui/modal";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import React, { useRef, useState } from "react";
import { moderateScale, verticalScale } from "react-native-size-matters";

const AccountCreated = () => {
  const [placement, setPlacement] = useState(undefined);
  const animation = useRef(null);
  const [open, setOpen] = useState(true);
  const navigation = useNavigation();

  const openModal = (placement) => {
    setOpen(true);
    setPlacement(placement);
  };

  return (
    <>
      <Modal isOpen={open} size="xl">
        <ModalBackdrop />
        <ModalContent className="max-w-96 rounded-xl bg-white">
          <ModalBody>
            <VStack className="items-center px-2 py-2">
              <Text
                style={{
                  fontSize: moderateScale(20),
                  color: "#6B7280",
                  fontFamily: "openSansBold",
                }}>
                Account created
              </Text>
              <LottieView
                autoPlay
                ref={animation}
                style={{
                  width: 200,
                  height: 200,
                  backgroundColor: "white",
                }}
                // Find more Lottie files at https://lottiefiles.com/featured
                source={require("../../assets/lottie/success.json")}
              />
              <Button
                className="w-full bg-orange-500 rounded-lg"
                style={{
                  marginTop: moderateScale(1),
                  height: verticalScale(48),
                }}
                onPress={() => {
                  navigation.navigate("Login");
                }}>
                <ButtonText
                  style={{
                    fontSize: moderateScale(15),
                    color: "white",
                    fontFamily: "openSansSemiBold",
                  }}>
                  Go to Sign In
                </ButtonText>
              </Button>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

export default AccountCreated;
