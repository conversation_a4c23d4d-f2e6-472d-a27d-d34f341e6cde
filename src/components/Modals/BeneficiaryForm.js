import { useNavigation } from "@react-navigation/native";
import { Button, ButtonText } from "@/components/ui/button";
import { FormControl } from "@/components/ui/form-control";
import { Input, InputField } from "@/components/ui/input";
import {
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalBody,
} from "@/components/ui/modal";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import React, { useEffect, useRef, useState } from "react";
import Toast from "react-native-root-toast";
import { moderateScale, verticalScale } from "react-native-size-matters";
import beneficiaries from "../../services/Beneficiaries/beneficiaries";
import LoadingModal from "../Loading/LoadingModal";

const BeneficiaryForm = ({
  status,
  openProp,
  close,
  beneficiaryPhoneNumber,
  beneficiaryBankAccount,
  beneficiaryName,
  bankCode,
  bankName,
  merchantName,
  merchantId,
  billerName,
  billerCode,
  billReferenceNumber,
}) => {
  const [placement, setPlacement] = useState(undefined);
  const animation = useRef(null);
  const navigation = useNavigation();
  const [name, setName] = useState();
  const [errorName, setErrorName] = useState();
  const [loading, setLoading] = useState(false);
  const [failed, setFailed] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [failed]);

  useEffect(() => {
    const interval = setTimeout(() => {
      setSuccess(false);
    }, 5000);
    return () => clearInterval(interval);
  }, [success]);

  const handleSubmit = () => {
    // check if beneficiary with that name already exist
    //check beneficiary status
    const data = {};
    switch (status) {
      case "individual":
        data.beneficiaryName = beneficiaryName ? beneficiaryName : name;
        data.beneficiaryPhoneNumber = beneficiaryPhoneNumber;
        data.beneficiaryBankAccount = beneficiaryBankAccount;
        data.bankCode = bankCode;
        data.bankName = bankName;
        Save(data);
        break;
      case "merchant":
        data.merchantName = merchantName;
        data.merchantId = merchantId;
        Save(data);
        break;
      case "biller":
        data.billerName = billerName;
        data.billerCode = billerCode;
        data.billReferenceNumber = billReferenceNumber;
        Save(data);
        break;
      default:
        break;
    }
  };

  const Save = async (data) => {
    try {
      close();
      setLoading(true);
      let response = await beneficiaries.add(data, status);
      //update local storage configs beneficiaries
      console.log(response);
      setLoading(false);
      setSuccess(true);
      setSuccessMessage("Beneficiary Saved Successfully");
    } catch (error) {
      setLoading(false);
      setFailed(true);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          setErrorMessage(error.response.data.message);
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  return (
    <>
      <LoadingModal isLoading={loading} />
      <Toast
        visible={success}
        position={30}
        shadow={true}
        animation={true}
        hideOnPress={true}
        backgroundColor={"green"}
        opacity={0.9}>
        {successMessage}
      </Toast>
      <Modal
        useRNModal={true}
        isOpen={openProp}
        safeAreaTop={true}
        size={"full"}
        onClose={close}>
        <Modal.Content maxWidth="350" rounded={"xl"} bg={"white"}>
          <Modal.Body>
            <VStack
              alignContent={"center"}
              space={moderateScale(20)}
              alignItems={"center"}
              px={2}
              py={2}>
              <Text
                fontSize={moderateScale(20)}
                color={"gray.600"}
                fontFamily={"openSansBold"}>
                Adding Beneficiary
              </Text>
              {status === "individual" && beneficiaryName === "" && (
                <FormControl>
                  <Input
                    fontFamily={"openSansSemiBold"}
                    onFocus={() => {
                      setErrorName("");
                    }}
                    focusOutlineColor={"#F68C1E"}
                    borderColor={errorName ? "red.400" : "gray.300"}
                    placeholder="Name"
                    fontSize={moderateScale(15)}
                    bgColor={"gray.100"}
                    rounded={"lg"}
                    onChangeText={(text) => setName(text)}
                    h={verticalScale(48)}
                  />
                  <Text color={"red.400"} fontSize={moderateScale(15)}>
                    {errorName}
                  </Text>
                </FormControl>
              )}

              <Button
                mt={moderateScale(1)}
                w={"100%"}
                onPress={() => {
                  handleSubmit();
                }}
                h={verticalScale(48)}
                bg="#F68C1E"
                rounded={"lg"}>
                <Text
                  fontSize={moderateScale(15)}
                  color="white"
                  fontFamily={"openSansSemiBold"}>
                  Save
                </Text>
              </Button>
            </VStack>
          </Modal.Body>
        </Modal.Content>
      </Modal>
    </>
  );
};

export default BeneficiaryForm;
