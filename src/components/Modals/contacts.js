import { Center } from "@/components/ui/center";
import { Divider } from "@/components/ui/divider";
import { Input, InputField } from "@/components/ui/input";
import {
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalBody,
} from "@/components/ui/modal";
import { Text } from "@/components/ui/text";
import { View } from "@/components/ui/view";
import React, { useEffect, useState } from "react";
import {
  Alert,
  PermissionsAndroid,
  Platform,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import * as Contacts from "expo-contacts";
import Toast from "react-native-root-toast";
import { moderateScale, verticalScale } from "react-native-size-matters";

const GetContacts = (props) => {
  const [placement, setPlacement] = useState(undefined);
  const [open, setOpen] = useState(true);
  const [contacts, setContacts] = useState([]);
  const [searchText, setSearchText] = useState("");
  const [loading, setLoading] = useState(false);
  const [failed, setFailed] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [failed]);

  useEffect(() => {
    const perm = async () => {
      const { status } = await Contacts.requestPermissionsAsync();
      if (status === "granted") {
        accessContacts();
      } else {
        setFailed(true);
        setErrorMessage("You don't have access to contacts");
      }
    };
    perm();
  }, []);

  const accessContacts = async () => {
    setLoading(true);
    try {
      const { data } = await Contacts.getContactsAsync();
      if (data.length > 0) {
        data.sort((a, b) => {
          const aName = a.name || a.firstName || "";
          const bName = b.name || b.firstName || "";
          return aName.toLowerCase().localeCompare(bName.toLowerCase());
        });
        setContacts(data);
      }
      setLoading(false);
    } catch (err) {
      setLoading(false);
      Alert.alert("Error", "Failed to access contacts");
    }
  };

  const filterData = (data) => {
    return data.filter((item) => {
      const name = item.name || item.firstName || "";
      return name.toLowerCase().includes(searchText.toLowerCase());
    });
  };

  const openModal = (placement) => {
    setOpen(true);
    setPlacement(placement);
  };

  return (
    <Modal
      useRNModal={true}
      isOpen={open}
      safeAreaTop={true}
      size={"full"}
      onClose={props.close}>
      <Toast
        visible={failed}
        position={30}
        shadow={true}
        animation={true}
        hideOnPress={true}
        backgroundColor={"red"}
        opacity={0.9}>
        {errorMessage}
      </Toast>
      <Modal.Content maxWidth="350" rounded={"xl"} bg={"white"}>
        <Modal.CloseButton />
        <Modal.Body>
          <View w="100%" h={"100%"} px={moderateScale(6)} pt={moderateScale(4)}>
            <Input
              fontFamily={"openSansSemiBold"}
              my={moderateScale(20)}
              value={searchText}
              focusOutlineColor={"#F68C1E"}
              borderColor={"gray.300"}
              placeholder="Search Contact"
              fontSize={moderateScale(15)}
              bgColor={"gray.100"}
              rounded={"lg"}
              onChangeText={(text) => setSearchText(text)}
              h={verticalScale(48)}
            />
            {loading && (
              <Center>
                <Text color={"blue.500"}>Loading ...</Text>
              </Center>
            )}

            {contacts ? (
              <View>
                {filterData(contacts).map((contact, index) => (
                  <View key={index}>
                    <View style={styles.MainContainer}>
                      <TouchableOpacity
                        onPress={() => {
                          props.setNumber(
                            contact.phoneNumbers?.[0]?.number
                              ?.toString()
                              .replace(/\s/g, "")
                              .replace("+263", "")
                          );
                          props.close();
                        }}>
                        <View style={styles.row}>
                          <View style={styles.avatarContainer}></View>
                          <View style={styles.listTextContainer}>
                            <View style={{ justifyContent: "center" }}>
                              <Text style={{ fontSize: 18 }}>
                                {" "}
                                {`${
                                  contact.name || contact.firstName || "Unknown"
                                }`}{" "}
                              </Text>
                            </View>
                          </View>
                        </View>
                      </TouchableOpacity>
                    </View>

                    <Divider />
                  </View>
                ))}
              </View>
            ) : (
              <View></View>
            )}
          </View>
        </Modal.Body>
      </Modal.Content>
    </Modal>
  );
};

const styles = StyleSheet.create({
  MainContainer: {
    flex: 1,
    paddingTop: Platform.OS === "ios" ? 20 : 0,
  },

  title: {
    padding: 12,
    fontSize: 22,
    backgroundColor: "#33691E",
    color: "white",
  },

  contactTitle: {
    fontSize: 22,
    paddingTop: 8,
    paddingBottom: 8,
    paddingLeft: 15,
    color: "black",
  },

  row: {
    flexDirection: "row",
    height: 60,
  },

  avatarContainer: {
    marginLeft: 12,
    justifyContent: "center",
    alignItems: "center",
  },

  listTextContainer: {
    marginLeft: 15,
    flexDirection: "row",
    flex: 1,
  },
});

export default GetContacts;
