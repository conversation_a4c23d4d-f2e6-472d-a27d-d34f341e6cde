import { useNavigation } from "@react-navigation/native";
import { Box } from "@/components/ui/box";
import { Center } from "@/components/ui/center";
import { HStack } from "@/components/ui/hstack";
import { Image } from "@/components/ui/image";
import {
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalBody,
} from "@/components/ui/modal";
import { Text } from "@/components/ui/text";
import { View } from "@/components/ui/view";
import React, { useEffect, useRef, useState } from "react";
import { Dimensions, SafeAreaView } from "react-native";
import { moderateScale } from "react-native-size-matters";
import { VirtualKeyboard } from "../../packages/VirtualKeyboard";
import Alert from "../Alert/alert";
import LoadingModal from "../Loading/LoadingModal";
const clear = require("../../assets/Icons/clear.png");

const ConfirmWithPin = (props) => {
  const height = Dimensions.get("window").height;
  const [loading, setLoading] = useState(false);
  const [failed, setFailed] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const navigation = useNavigation();
  const pinRef = useRef(null);
  const [placement, setPlacement] = useState(undefined);
  const animation = useRef(null);
  const [open, setOpen] = useState(props.openConfirm);

  const openModal = (placement) => {
    setOpen(true);
    setPlacement(placement);
  };

  const goBack = () => {
    navigation.goBack();
  };
  const [pins, setPin] = useState("");

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);
    return () => clearInterval(interval);
  }, [failed]);

  return (
    <SafeAreaView>
      <Modal
        isOpen={true}
        safeAreaTop={true}
        size={"full"}
        onClose={props.close}
        useRNModal={true}
        animationPreset="slide">
        <Modal.Content
          maxHeight={height}
          maxWidth={350}
          rounded={"xl"}
          bg={"white"}>
          <Modal.CloseButton />
          <Modal.Body>
            <View
              bg={"white"}
              style={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
                height: "100%",
              }}>
              <View px={moderateScale(20)} py={moderateScale(10)}>
                <LoadingModal isLoading={loading} />
                {failed ? (
                  <Alert status={"error"} error={errorMessage} show={true} />
                ) : (
                  <></>
                )}
                <Center>
                  <HStack
                    alignItems={"center"}
                    space={moderateScale(6)}
                    mt={moderateScale(50)}>
                    <Text
                      fontFamily={"openSansBold"}
                      fontSize={moderateScale(15)}
                      color={"#252622"}
                      fontWeight="500">
                      Confirm With Pin
                    </Text>
                  </HStack>
                </Center>
              </View>
              <Box h={100}>
                <HStack justifyContent={"center"}>
                  {pins.split("").map((item, key) => {
                    return (
                      <Text
                        key={key}
                        fontFamily={"openSansBold"}
                        fontSize={moderateScale(30)}
                        color={"#F68C1E"}
                        fontWeight="500">
                        {item}
                      </Text>
                    );
                  })}
                </HStack>
              </Box>
              <Box overflow={"hidden"}>
                <View mb={moderateScale(24)}>
                  <VirtualKeyboard
                    ref={pinRef}
                    onChange={(pin) => {
                      if (pin.split("").length === 4) {
                        props.handleRequest(pin);
                        setPin("");
                        return;
                      }
                      setPin(pin);
                    }}
                    keyStyle={{
                      backgroundColor: "#f2f6fb",
                      margin: 20,
                      borderRadius: 100,
                      height: 60,
                    }}
                    keyTextStyle={{
                      fontSize: 24,
                      color: "#F68C1E",
                      fontFamily: "openSansBold",
                    }}
                    keyboardCustomBackKey={
                      <Image
                        source={clear}
                        alt="clear"
                        style={{ width: 20, height: 20 }}
                      />
                    }
                  />
                </View>
              </Box>
            </View>
          </Modal.Body>
        </Modal.Content>
      </Modal>
    </SafeAreaView>
  );
};

export default ConfirmWithPin;
