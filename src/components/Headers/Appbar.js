import { Ionicons } from "@expo/vector-icons";
import { HStack } from "@/components/ui/hstack";
import { View } from "@/components/ui/view";
import React from "react";
import { TouchableOpacity } from "react-native";
import { scale } from "react-native-size-matters";

const Appbar = ({ goBack }) => {
  return (
    <View>
      <HStack className="bg-transparent py-3 justify-between items-center w-full">
        <HStack className="items-center">
          <TouchableOpacity onPress={goBack}>
            <Ionicons
              name="arrow-back-outline"
              size={scale(16)}
              color="black"
            />
          </TouchableOpacity>
        </HStack>
      </HStack>
    </View>
  );
};

export default Appbar;
