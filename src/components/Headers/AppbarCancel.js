import { Feather } from "@expo/vector-icons";
import { View } from "@/components/ui/view";
import { Text } from "@/components/ui/text";
import { HStack } from "@/components/ui/hstack";
import React from "react";
import { TouchableOpacity } from "react-native";
import { moderateScale } from "react-native-size-matters";

const AppbarCancel = ({ goBack, title }) => {
  return (
    <View>
      <HStack className="bg-white py-3 justify-between items-center w-full">
        <HStack className="items-center w-full justify-between">
          <TouchableOpacity onPress={goBack}>
            <Feather name="x" size={24} color="gray" />
          </TouchableOpacity>
          <View>
            <Text
              style={{
                fontFamily: "openSansBold",
                fontSize: moderateScale(16),
                color: "#252622",
                fontWeight: "500",
              }}>
              {title}
            </Text>
          </View>
          <View></View>
        </HStack>
      </HStack>
    </View>
  );
};

export default AppbarCancel;
