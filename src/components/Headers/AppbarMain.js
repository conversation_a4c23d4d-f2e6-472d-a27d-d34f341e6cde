import {
  AntDesign,
  MaterialCommunityIcons,
  FontAwesome,
  FontAwesome5,
} from "@expo/vector-icons";
import { Avatar, AvatarFallbackText } from "@/components/ui/avatar";
import { HStack } from "@/components/ui/hstack";
import { Image } from "@/components/ui/image";
import { Text } from "@/components/ui/text";
import { View } from "@/components/ui/view";
import React from "react";
import { TouchableOpacity } from "react-native";
import { moderateScale, verticalScale, scale } from "react-native-size-matters";
import netone from "../../assets/Icons/netone.png";

const AppbarMain = ({ goBack, greeting, name, phone, logout, initials }) => {
  return (
    <View>
      <HStack
        className="bg-transparent justify-between items-center w-full"
        style={{ paddingVertical: moderateScale(4) }}>
        <HStack
          className="bg-transparent justify-between items-center w-full"
          style={{ paddingVertical: moderateScale(15) }}>
          <HStack className="items-center" style={{ gap: moderateScale(10) }}>
            <View
              className="justify-center items-center bg-white rounded-full"
              style={{
                padding: moderateScale(10),
                width: moderateScale(50),
                height: moderateScale(50),
              }}>
              <FontAwesome5
                name="user-alt"
                size={moderateScale(20)}
                color="#949494"
              />
            </View>
            <View>
              <Text
                style={{
                  fontFamily: "openSansBold",
                  fontSize: moderateScale(15),
                  color: "white",
                }}>
                Hello, {"263" + phone}
              </Text>
            </View>
          </HStack>

          <View
            className="overflow-hidden justify-center items-center bg-transparent rounded-xl"
            style={{
              padding: moderateScale(1),
              width: moderateScale(80),
              height: moderateScale(50),
            }}>
            <Image
              source={netone}
              style={{
                height: verticalScale(50),
                width: scale(70),
              }}
              alt="OneMoney"
            />
          </View>
        </HStack>
      </HStack>
    </View>
  );
};

export default AppbarMain;
