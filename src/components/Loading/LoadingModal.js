import { Modal, ModalBackdrop, ModalContent } from "@/components/ui/modal";
import { Spinner } from "@/components/ui/spinner";
import { View } from "@/components/ui/view";
import React from "react";

const LoadingModal = ({ isLoading }) => {
  return (
    <Modal isOpen={isLoading} closeOnOverlayClick={false}>
      <ModalBackdrop />
      <ModalContent>
        <View className="p-4 rounded-2xl bg-white">
          <Spinner size="large" color="#F68C1E" />
        </View>
      </ModalContent>
    </Modal>
  );
};

export default LoadingModal;
