import { View } from "@/components/ui/view";
import { Text } from "@/components/ui/text";
import React, { useState } from "react";
import { TouchableOpacity, StyleSheet } from "react-native";
import { moderateScale } from "react-native-size-matters";

const CustomTooltip = ({ children, text }) => {
  const [showTooltip, setShowTooltip] = useState(false);

  return (
    <View>
      <TouchableOpacity
        onPress={() => setShowTooltip(!showTooltip)}
        onBlur={() => setShowTooltip(false)}
        onFocus={() => setShowTooltip(true)}
        style={{
          width: moderateScale(30),
        }}>
        {children}
      </TouchableOpacity>
      {showTooltip && (
        <View
          className="bg-gray-500 top-1 rounded-lg"
          style={{ padding: moderateScale(8) }}>
          <Text
            style={{
              color: "white",
              fontFamily: "openSans",
            }}>
            {text}
          </Text>
        </View>
      )}
    </View>
  );
};

export default CustomTooltip;
