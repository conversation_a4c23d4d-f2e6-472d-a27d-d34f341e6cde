import { Input, InputField } from "@/components/ui/input";
import { Text } from "@/components/ui/text";
import React, { useState } from "react";
import { moderateScale, verticalScale } from "react-native-size-matters";

const CurrencyInputField = ({ decimalPlaces = 2, onChange, error }) => {
  const [value, setValue] = useState("");

  const handleChange = (text) => {
    const cleanedText = text.replace(/[^0-9.]/g, "");

    // Remove leading zeros
    const withoutLeadingZeros = cleanedText.replace(/^0+/, "");

    const decimalIndex = withoutLeadingZeros.indexOf(".");
    const hasDecimal = decimalIndex !== -1;

    if (hasDecimal) {
      const numDecimalPlaces = withoutLeadingZeros.length - decimalIndex - 1;
      if (numDecimalPlaces > decimalPlaces) {
        setValue(
          withoutLeadingZeros.slice(0, decimalIndex + decimalPlaces + 1)
        );
        return;
      }
    }

    setValue(withoutLeadingZeros);

    if (onChange) {
      onChange(withoutLeadingZeros);
    }
  };

  return (
    <>
      <Input
        className={`${
          error ? "border-red-400" : "border-gray-300"
        } bg-gray-100 rounded-lg`}
        style={{ height: verticalScale(48) }}>
        <InputField
          keyboardType="numeric"
          value={value}
          onChangeText={handleChange}
          placeholder="Amount"
          style={{
            fontFamily: "openSansMedium",
            fontSize: moderateScale(15),
          }}
        />
      </Input>

      {error && (
        <Text
          style={{
            color: "#EF4444",
            fontSize: moderateScale(15),
          }}>
          {error}
        </Text>
      )}
    </>
  );
};

export default CurrencyInputField;
