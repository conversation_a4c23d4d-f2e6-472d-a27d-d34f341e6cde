import { Alert, AlertIcon, AlertText } from "@/components/ui/alert";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { View } from "@/components/ui/view";
import React from "react";

const Alerts = ({ status, show, error }) => {
  return (
    <View>
      <Alert className="w-full" action={status} variant="top-accent">
        <VStack className="flex-shrink w-full" space="sm">
          <HStack
            className="flex-shrink items-center justify-between"
            space="sm">
            <HStack className="flex-shrink items-center" space="sm">
              <AlertIcon />
              <AlertText>{error}</AlertText>
            </HStack>
          </HStack>
        </VStack>
      </Alert>
    </View>
  );
};

export default Alerts;
