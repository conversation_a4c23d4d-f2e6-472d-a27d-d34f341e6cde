import axios from "../../utilities/Axios";

class BalanceService {
  async getBalance(phone) {
    return axios.get(`/telco-mobile-erp/selfcare/v1/balance-enquiry/${phone}`, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }

  async getBalancePostPaid(phone) {
    return axios.get(
      `/zsmart-core/mobile-erp/selfcare/postpaid/balance-enquiry/${phone}`,
      {
        headers: {
          "Content-type": "application/json",
        },
      }
    );
  }
}

export default new BalanceService();
