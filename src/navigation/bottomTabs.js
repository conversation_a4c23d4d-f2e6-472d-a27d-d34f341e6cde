import {
  Entypo,
  FontAwesome,
  MaterialCommunityIcons,
  MaterialIcons,
} from "@expo/vector-icons";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { useNavigation } from "@react-navigation/native";
import { useEffect, useState } from "react";
import { Alert, Platform, StyleSheet } from "react-native";
import { moderateVerticalScale } from "react-native-size-matters";
import LocalStore from "../utilities/store";
import Main4 from "../screens/Main";
import { navigationref, isreadyref, navigate } from "./rootNav";
import Services from "../screens/Services";
import Account from "../screens/Account";
import SelfCare from "../screens/SelfCare";
import FAQ from "../screens/FAQ/faq";

const Tab = createBottomTabNavigator();

function Tabs() {
  const navigation = useNavigation();
  const [logoutClicked, setLogoutClicked] = useState(false);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    return () => {
      isreadyref.current = false;
    };
  }, []);

  const logout = async () => {
    setLogoutClicked(true);
    const store = await LocalStore.deleteData("@userToken");
    await LocalStore.deleteData("@bundles");
    if (store == "success") {
      navigation.navigate("Landing");
    } else {
      console.log("logout failed");
    }
  };

  return (
    <>
      <Tab.Navigator
        onready={() => {
          isreadyref.current = true;
        }}
        initialRouteName="Main"
        screenOptions={{
          tabBarShowLabel: true,

          tabBarLabelStyle: {
            fontSize: moderateVerticalScale(14),
            fontFamily: "openSansSemiBold",
          },
          tabBarStyle:
            Platform.OS === "android"
              ? {
                  backgroundColor: "white",
                  position: "absolute",
                  height: moderateVerticalScale(55),
                  elevation: 0,
                  position: "absolute",
                  paddingBottom: 5,
                  borderColor: "orange",
                  borderTopColor: "orange",
                  borderTopWidth: 1,
                }
              : {},
        }}>
        <Tab.Screen
          listeners={{
            beforeRemove: (e) => {
              if (logoutClicked === false) {
                e.preventDefault();
                Alert.alert(
                  "Logging Out",
                  "Do you want to logout ?",
                  [
                    {
                      text: "No",
                      style: "cancel",
                    },
                    { text: "Yes", onPress: () => logout() },
                  ],
                  { cancelable: false }
                );
              }
            },
          }}
          options={{
            headerShown: false,
            tabBarLabel: "Home",
            tabBarIcon: ({ color, size }) => (
              <MaterialCommunityIcons name="home" color={color} size={size} />
            ),
            tabBarActiveTintColor: "#F68C1E",
            tabBarInactiveTintColor: "#737373",
          }}
          name="Main"
          component={Main4}
          initialParams={{ current: "Main" }}
        />

        <Tab.Screen
          options={{
            headerShown: true,
            tabBarLabel: "Self Service",
            headerTitle: "Self Service",
            headerStyle: {
              backgroundColor: "#F68C1E",
            },
            headerTintColor: "white",
            headerTitleStyle: {
              marginLeft: moderateVerticalScale(16),
              fontSize: moderateVerticalScale(20),
            },
            tabBarIcon: ({ color, size }) => (
              <MaterialCommunityIcons
                name="hand-extended"
                color={color}
                size={size}
              />
            ),

            tabBarActiveTintColor: "#F68C1E",
            tabBarInactiveTintColor: "#737373",
          }}
          name="Services"
          component={Services}
        />

        <Tab.Screen
          options={{
            headerShown: true,
            headerTitle: "FAQ",
            headerStyle: {
              backgroundColor: "#F68C1E",
            },
            headerTintColor: "white",
            headerTitleStyle: {
              marginLeft: moderateVerticalScale(16),
              fontSize: moderateVerticalScale(20),
            },
            tabBarIcon: ({ color, size }) => (
              <MaterialCommunityIcons
                name="comment-question"
                color={color}
                size={size}
              />
            ),
            tabBarActiveTintColor: "#F68C1E",
            tabBarInactiveTintColor: "#737373",
          }}
          name="FAQ"
          component={FAQ}
        />

        <Tab.Screen
          options={{
            headerShown: true,
            headerTitle: "Account",
            headerStyle: {
              backgroundColor: "#F68C1E",
            },
            headerTintColor: "white",
            headerTitleStyle: {
              marginLeft: moderateVerticalScale(16),
              fontSize: moderateVerticalScale(20),
            },
            tabBarIcon: ({ color, size }) => (
              <MaterialCommunityIcons
                name="account"
                color={color}
                size={size}
              />
            ),
            tabBarActiveTintColor: "#F68C1E",
            tabBarInactiveTintColor: "#737373",
          }}
          name="Account"
          component={Account}
        />
      </Tab.Navigator>
    </>
  );
}

const styles = StyleSheet.create({
  shadow: {
    shadowColor: "#F68C1E",
    shadowOffset: {
      width: 10,
      height: 15,
    },
    shadowOpacity: 0.95,
    shadowRadius: 10,
    elevation: 6,
  },
});

export default Tabs;
