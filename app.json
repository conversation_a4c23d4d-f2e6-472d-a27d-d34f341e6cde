{"expo": {"name": "Netone", "slug": "Netone", "version": "1.0.0", "orientation": "portrait", "assetBundlePatterns": ["**/*", "assets/fonts/*"], "icon": "./src/assets/Icons/NetoneIcon.png", "splash": {"image": "./src/assets/SplashScreen/splash.png", "resizeMode": "cover", "backgroundColor": "#ffffff"}, "android": {"adaptiveIcon": {"foregroundImage": "./src/assets/Icons/NetoneAdaptiveIcon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.READ_CONTACTS", "android.permission.WRITE_CONTACTS", "android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE"], "package": "com.emacliam.Netone", "versionCode": 1, "edgeToEdgeEnabled": true}, "plugins": [["expo-contacts", {"contactsPermission": "Allow $(PRODUCT_NAME) to access your contacts."}], ["expo-build-properties", {"android": {"compileSdkVersion": 34, "targetSdkVersion": 34, "buildToolsVersion": "33.0.0", "usesCleartextTraffic": true}}], "expo-font"], "extra": {"eas": {"projectId": "5545f4a7-7aed-4ec5-8db1-a191cc950a2e"}}, "ios": {"infoPlist": {"NSContactsUsageDescription": "Allow $(PRODUCT_NAME) to access your contacts."}, "bundleIdentifier": "com.emacliam.Netone"}}}