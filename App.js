import { StatusBar } from "expo-status-bar";
import "@/global.css";
import { GluestackUIProvider } from "@/components/ui/gluestack-ui-provider";
import { Alert, AlertIcon, AlertText } from "@/components/ui/alert";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { View } from "@/components/ui/view";
import { Feather } from "@expo/vector-icons";
import React, { useEffect, useState } from "react";
import Navigation from "./src/navigation/Stack";
import { useFonts } from "expo-font";
import NetInfo from "@react-native-community/netinfo";
import { RootSiblingParent } from "react-native-root-siblings";

export default function App() {
  const [connected, setConnected] = useState(true);
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      if (!state.isConnected) {
        setConnected(false);
      } else {
        setConnected(true);
      }
    });
    return () => {
      unsubscribe();
    };
  }, []);

  const [loaded] = useFonts({
    openSans: require("./src/assets/fonts/open.ttf"),
    openSansBold: require("./src/assets/fonts/OpenSans-Bold.ttf"),
    openSansExtraBold: require("./src/assets/fonts/OpenSans-ExtraBold.ttf"),
    openSansSemiBold: require("./src/assets/fonts/OpenSans-SemiBold.ttf"),
    openSansMedium: require("./src/assets/fonts/OpenSans-Medium.ttf"),
  });

  if (!loaded) {
    return null;
  }

  return (
    <GluestackUIProvider mode="light">
      <RootSiblingParent>
        <View
          style={{
            flex: 1,
            backgroundColor: "#F68C1E",
          }}>
          <StatusBar style="dark" backgroundColor="#F68C1E" />
          <Navigation />
          {!connected && (
            <Alert action="warning" variant="solid">
              <AlertIcon />
              <AlertText>Poor internet connection</AlertText>
            </Alert>
          )}
        </View>
      </RootSiblingParent>
    </GluestackUIProvider>
  );
}
